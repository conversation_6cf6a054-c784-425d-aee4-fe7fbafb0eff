<h1><b> Integração de Viagem (V2) </b></h1>

<br> <h4>Método responsável pela integração de viagem no formato V2 (versão 2), a qual, através de uma única chamada/requisição o sistema realiza a integração da viagem 
bem como realiza o processo de cadastrar todos os cadastros necessários para inserir uma viagem (motorista, vincular cartão motorista, proprietário, vincular 
cartão proprietário, veiculo - cavalo,  carreta(s) ou utilitários -, cliente origem e cliente destino.</h4>

<br>Através deste método é possível:

+ Geração de CIOT (somente declarar o CIOT);
+ Geração de CIOT, Pagamento de Frete (Cartão ou Conta Virtual);
+ Geração de CIOT, Pagamento de Frete (Depósito) e Vale Pedágio TAG;
+ Geração de CIOT, Pagamento de Frete (Cartão ou Conta Virtual) e Vale Pedágio TAG
+ Geração de Viagem para Vale Pedágio Avulso (sem emissão de CIOT e Pagamento de Frete no Cartão ou Conta Virtual);
+ Solicitação e Cancelamento de Crédito de Abastecimento (TicketLog e Shell Expers);

<br> Regras:

+ __CIOT PADRÃO__: Cancelamento de uma Operação de Transporte: a Operação de Transporte, desde que 
não tenha sido consultada pela fiscalização da ANTT, poderá ser cancelada em até 24h após a data de emissão. 

+ __CIOT PERIÓDICO (TAC AGREGADO)__: Será permitido o cancelamento do cadastro da Operação de Transporte do tipo
TAC-agregado, desde que ele não tenha sido consultado pela fiscalização da ANTT, em até 5 dias
da abertura.

<br>

## <b>Regras para Abastecimento</b>

+ __SOLICITAÇÃO DE CRÉDITO__: Para solicitar crédito de abastecimento, deve ser enviada uma parcela com TipoEvento = 5 (Abastecimento), Status = 2 (Baixado) e HabilitarPagamentoCartao = false. O processo comprará o crédito na TicketLog ou Shell Expers, não realizará pagamento PIX.

+ __PARCELAS NOVAS__: O processo de solicitar/inserir crédito se baseia em parcelas novas (quando IdViagemEvento = NULL). Em caso de retificação de viagem já cadastrada, não enviar novamente a parcela de ABASTECIMENTO para evitar duplicação.

+ __ALTERAÇÃO DE PARCELA__: Para alterar uma parcela de abastecimento, deve ser enviado um cancelamento baseado no IdViagemEvento e uma nova parcela.

+ __CANCELAMENTO DE CRÉDITO__: Para cancelar crédito, é necessário que já exista uma parcela com solicitação de crédito enviada. Deve ser enviado o método viagem/integrar com Status = 3 (Cancelado) e o IdViagemEvento da parcela a ser cancelada.

+ __PLACA DO VEÍCULO__: A placa enviada para TicketLog ou Shell Expers será o campo "Placa" enviado no JSON da Viagem.

<br>

## <b>Request</b>

<br>

   * __ENDPOINT: ViagemV2/Integrar__ 
   * __VERBO: POST__

<br>

| Índice        | Campo                        | Descrição                                                                                                                                                                             | Elem. | Tipo        | Ocor. |   Tam.    | Versão |
|---------------|------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------|-------------|-------|-----------|--------|
| 1             | Token                        | Token de autenticação                                                                                                                                                                 | E     | Caractere   | 1-1   | 100       | 1.0    |
| 2             | CNPJAplicacao                | CNPJ para autenticação                                                                                                                                                                | E     | Caractere   | 1-1   | 14        | 1.0    |
| 3             | CNPJEmpresa                  | CNPJ da empresa responsável pela requisição                                                                                                                                           | E     | Caractere   | 1-1   | 14        | 1.0    |
| 4             | DocumentoUsuarioAudit        | Documento do usuário que está realizando a operação                                                                                                                                   | E     | Caractere   | 1-1   | 14        | 1.0    |
| 5             | NomeUsuarioAudit             | Nome do usuário que está realizando a operação                                                                                                                                        | E     | Caractere   | 1-1   | 60        | 1.0    |
| 6             | DadosViagem                  | Objeto pai responsável pelos objetos filhos que compõe a viagem                                                                                                                       | G     |             | 1-1   |           | 1.0    |
| 6.1           | DadosIniciais                | Objeto responsável pelos dados iniciais da viagem                                                                                                                                     | G     |             | 1-1   |           | 1.0    |
| 6.1.1         | NumeroControle               | Número para controle no ERP                                                                                                                                                           | E     | Caractere   | 1-0   | 100       | 1.0    |
| 6.1.2         | StatusViagem                 | Enum de status de viagem, pondendo ser eles:<br/>__1:__ Aberto (Padrão);<br/>__2:__ Programada;<br/>__3:__ Cancelada;<br/>__4:__ Bloqueada;<br/>__5:__ Baixada.                       | E     | Inteiro     | 1-0   | 1         | 1.0    |
| 6.1.3         | DeclaracaoCiot               | Enumeração para declaração de CIOT, pondendo ser:<br/>__0:__ Não habilita a declaração de Ciot na viagem; <br/>__1:__ Habilita a declaração de Ciot na viagem; <br/>__2:__ Força habilitação de declaração de Ciot para não equiparados na viagem. | E     | Inteiro     | 1-1   | 1       | 1.0    |
| 6.1.4         | Produto                      | Descrição do produto da viagem                                                                                                                                                        | E     | Caractere   | 1-0   | 100       | 1.0    |
| 6.1.5         | UnidadeMedidaProduto         | Enumeração da unidade de medida do produto, podendo ser: <br/>__0:__ Peso;<br/>__1:__ Saca.                                                                                           | E     | Inteiro     | 1-1   | 1         | 1.0    |
| 6.1.6         | NaturezaCarga                | Código nacional de natureza de carga (Utilizado para a emissão de CIOT)                                                                                                               | E     | Inteiro     | 1-0   | 10        | 1.0    |
| 6.1.7         | GerarCiotTacAgregado         | Flag utilizada para não precisar da consulta no cadastro do proprietário para gerar CIOT do tipo TAC Agregado, além disso atualiza o registro do proprietário com o que for informado | E     | Booleano    | 1-0   | True/False| 1.0    |
| 6.1.8         | RealizarIntegracoesPreViagem | Flag utilizada para informar se deseja que a API realize os cadastros de forma automática, usar default TRUE                                                                          | E     | Booleano    | 1-0   | True/False| 1.0    |
| 6.2           | Documentos                   | Objeto responsável pelos dados que contém os documentos (CPF/CNPJ) da viagem                                                                                                          | G     |             | 1-1   |           | 1.0    |
| 6.2.1         | ClienteOrigemDocumento       | CPF/CNPJ do cliente de origem que compõe a viagem                                                                                                                                     | E     | Caractere   | 1-1   | 14        | 1.0    |
| 6.2.2         | ClienteDestinoDocumento      | CPF/CNPJ do cliente de destino que compõe a viagem                                                                                                                                    | E     | Caractere   | 1-1   | 14        | 1.0    |
| 6.2.3         | ClienteTomadorDocumento      | CPF/CNPJ do cliente tomador que compõe a viagem                                                                                                                                       | E     | Caractere   | 1-0   | 14        | 1.0    |
| 6.2.4         | FilialDocumento              | CNPJ da Filial que compõe a viagem                                                                                                                                                    | E     | Caractere   | 1-0   | 14        | 1.0    |
| 6.2.5         | MotoristaDocumento           | CPF do Motorista que compõe a viagem                                                                                                                                                  | E     | Caractere   | 1-1   | 11        | 1.0    |
| 6.2.6         | ProprietarioDocumento        | CPF/CNPJ do Proprietário que compõe a viagem                                                                                                                                          | E     | Caractere   | 1-1   | 11        | 1.0    |
| 6.2.7         | NumeroDocumentoNotaCliente   | Número do documento na nota (numeração para o cliente)                                                                                                                                | E     | Caractere   | 1-0   | 100       | 1.0    |
| 6.2.6         | NumeroDocumentoFiscal        | Número do documento fiscal                                                                                                                                                            | E     | Caractere   | 1-0   | 100       | 1.0    |
| 6.3           | Valores                      | Objeto responsável pelos valores da viagem                                                                                                                                            | G     |             | 1-1   |           | 1.0    |
| 6.3.1         | Irrpf                        | Valor do Imposto de Renda Retido na Fonte                                                                                                                                             | E     | Decimal     | 1-1   | 10,2      | 1.0    |
| 6.3.2         | Inss                         | Valor do Instituto Nacional do Seguro Social                                                                                                                                          | E     | Decimal     | 1-1   | 10,2      | 1.0    |
| 6.3.3         | SestSenat                    | Valor do Serviço Social do Transporte e o Serviço Nacional de Aprendizagem do Transporte                                                                                              | E     | Decimal     | 1-1   | 10,2      | 1.0    |
| 6.3.4         | PesoSaida                    | Peso do frete detalhado no documento do transporte                                                                                                                                    | E     | Decimal     | 1-1   | 10,3      | 1.0    |
| 6.3.5         | ValorMercadoria              | Valor total da mercadoria                                                                                                                                                             | E     | Decimal     | 1-0   | 10,2      | 1.0    |
| 6.3.6         | Quantidade                   | Quantidade referente a carga                                                                                                                                                          | E     | Decimal     | 1-1   | 18,2      | 1.0    |
| 6.4           | Enderecos                    | Objeto com os dados dos endereços que compõe a viagem                                                                                                                                 | G     |             | 1-1   |           | 1.0    |
| 6.4.1         | EnderecoColeta               | Endereço de coleta da mercadoria                                                                                                                                                      | E     | Caractere   | 1-0   | 100       | 1.0    |
| 6.4.2         | EnderecoEntrega              | Endereço de entrega da mercadoria                                                                                                                                                     | E     | Caractere   | 1-0   | 100       | 1.0    |
| 6.5           | DadosPagamento               | Objeto com os dados dos dados de pagamento que compõe a viagem                                                                                                                        | G     |             | 1-0   |           | 1.0    |
| 6.5.1         | FormaPagamento               | Enum de forma de pagamento, podendo ser: <br/>__1:__ Cartao; <br/>__2:__ Conta Corrente;<br/>__3:__ Conta Poupança;<br/>__4:__ Conta Pagamento;<br/>__5:__ Outros                     | E     | Inteiro     | 1-0   | 1         | 1.0    |
| 6.5.2         | CodigoBacen                  | Codigo do banco registrado no Banco Central                                                                                                                                           | E     | Caractere   | 1-0   | 5         | 1.0    |
| 6.5.3         | Agencia                      | Agencia                                                                                                                                                                               | E     | Inteiro     | 1-0   | 10        | 1.0    |
| 6.5.4         | Conta                        | Dados da Conta corrente                                                                                                                                                               | E     | Caractere   | 1-0   | 20        | 1.0    |
| 6.5.5         | TipoConta                    | Enum de tipo de conta, podendo ser: <br/>__0:__ ContaCorrente; <br/>__1:__ ContaPoupanca.                                                                                             | E     | Inteiro     | 1-0   | 1         | 1.0    |
| 6.6           | Veiculo                      | Objeto com os dados do veículo                                                                                                                                                        | G     |             | 1-1   |           | 1.0    |
| 6.6.1         | Rntrc                        | Registro nacional de transportes rodoviários de cargas do veículo                                                                                                                     | E     | Inteiro     | 1-0   | 10        | 1.0    |
| 6.6.2         | Placa                        | Placa do veículo                                                                                                                                                                      | E     | Caractere   | 1-1   | 7         | 1.0    |
| 6.7           | Datas                        | Objeto com as datas das que compõe a viagem                                                                                                                                           | G     |             | 1-0   |           | 1.0    |
| 6.7.1         | DataColeta                   | Data da coleta da carga (carregamento)                                                                                                                                                | E     | Data        | 1-0   | yyyy-MM-dd HH:mm:ss| 1.0    |
| 6.7.2         | DataPrevisaoEntrega          | Data de previsão de entrega da carga (descarregamento)                                                                                                                                | E     | Data        | 1-0   | yyyy-MM-dd HH:mm:ss| 1.0    |
| 6.7.3         | DataEmissaoDocumentosFiscal  | Data que foi emitido o documento fiscal                                                                                                                                               | E     | Data        | 1-0   | yyyy-MM-dd HH:mm:ss| 1.0    |
| 6.8           | ViagemEventos                | Array de Objetos com os dados das parcelas que compõe a viagem                                                                                                                        | A     |             | 1-1   |           | 1.0    |
| 6.8.1         | IdViagemEvento               | Código do evento da viagem – deve ser armazenado este Id para retificar/alterar o respectivo evento. Para parcelas novas de abastecimento, enviar NULL.                             | E     | Inteiro     | 1-0   | 10        | 1.0    |
| 6.8.2         | HabilitarPagamentoCartao     | Ao definir o parâmetro de pagamento como __true__, a transação será processada via Extratta (Cartão).<br/> Caso o proprietário não tenha um cartão vinculado, o sistema criará automaticamente uma conta virtual, na qual o valor será disponibilizado.<br/> Se o parâmetro pagamento for definido como __false__, dois cenários são possíveis:<br/>__1-__ O pagamento poderá ser registrado como depósito (transação não irá ocorrer pela Extratta);<br/>__2-__ O pagamento poderá ser realizado via PIX através da Extratta, desde que o cliente tenha contratado o serviço de pagamento via PIX e o proprietário tenha uma chave PIX válida cadastrada.<br/>**Para abastecimento:** Sempre enviar false.        | E     | Booleano    | 1-1   | True/False| 1.0    |
| 6.8.3         | NumeroControle               | Número de controle para ERP. Para abastecimento, enviar documento de controle.                                                                                                        | E     | Caractere   | 1-0   | 100       | 1.0    |
| 6.8.4         | TipoEvento                   | Enumeração do tipo de parcela, podendo ser: <br/>__0:__ Adiantamento;<br/>__1:__ Saldo;<br/>__2:__ Estadia;<br/>__3:__ RPA;<br/>__4:__ Tarifa ANTT;<br/>__5:__ Abastecimento;<br/>__6:__ Abono;        | E     | Inteiro     | 1-0   | 1         | 1.0    |
| 6.8.5         | ValorPagamento               | Valor do pagamento da viagem. Para abastecimento, será usado para enviar o valor do crédito para TicketLog ou Shell Expers.                                                          | E     | Decimal     | 1-1   | 10,3      | 1.0    |
| 6.8.6         | Instrucao                    | Instrução do evento da viagem/Observações. Para abastecimento, sugerido: "Compra Crédito Abastecimento".                                                                             | E     | Caractere   | 1-0   | 100       | 1.0    |
| 6.8.5         | DataValidade                 | Data de validade                                                                                                                                                                      | E     | Data        | 1-0   | yyyy-MM-dd| 1.0    |
| 6.8.6         | NumeroRecibo                 | Número do recibo                                                                                                                                                                      | E     | Caractere   | 1-0   | 20        | 1.0    |
| 6.8.7         | Instrucao                    | Instruções                                                                                                                                                                            | E     | Caractere   | 1-0   | 100       | 1.0    |
| 6.8.8         | Status                       | Status do evento de pagamento:<br/>__0:__ Aberto/Pendente;<br/>__1:__ Bloqueado;<br/>__2:__ Baixado (Efetivado/Liquidado);<br/>__3:__ Cancelado;<br/>__5:__ Agendado.<br/>**Para abastecimento:** Status 2 = Solicitar crédito; Status 3 = Cancelar crédito.                                                                                                                           | E     | Inteiro     | 1-1   | 1         | 1.0    |
| 6.8.9         | DataAgendamentoPagamento     | Data para agendamento de pagamento automático no formato dd/MM/yyyy, é obrigatório o uso em conjunto com o status Agendado.                                                           | E     | Data        | 1-0   | yyyy-MM-dd| 1.0    |
| 6.8.10        | DadosAbastecimento           | Objeto com dados específicos para solicitação/cancelamento de crédito de abastecimento. Obrigatório quando TipoEvento = 5 (Abastecimento).                                          | G     |             | 1-0   |           | 1.0    |
| ********      | CodigoCredito                | Será usado para enviar o CodigoCredito na TicketLog ou Shell Expers                                                                                                                   | E     | Caractere   | 1-0   | 50        | 1.0    |
| ********      | CodigoCliente                | Pegará do cadastro de empresa - Código do cliente na TicketLog ou Shell Expers                                                                                                        | E     | Caractere   | 1-0   | 50        | 1.0    |
| ********      | CodigoProduto                | Enviar o código do produto da TicketLog ou Shell Expers (deve ser enviado pelo TMS)                                                                                                   | E     | Caractere   | 1-1   | 50        | 1.0    |
| ********      | numerocartao                 | Enviar o número do cartão na TicketLog ou Shell Expers                                                                                                                                | E     | Caractere   | 1-0   | 50        | 1.0    |
| 6.8.10.5      | DataValidade                 | Enviar data de validade para a TicketLog ou Shell Expers no formato yyyy-MM-dd                                                                                                        | E     | Data        | 1-0   | yyyy-MM-dd| 1.0    |
| 6.8.10.6      | DataLiberacao                | Enviar data de liberação para a TicketLog ou Shell Expers no formato yyyy-MM-dd                                                                                                       | E     | Data        | 1-0   | yyyy-MM-dd| 1.0    |
| 6.8.10.7      | Fornecedor                   | Fornecedor de abastecimento: __0:__ TicketLog; __1:__ Shell Expers                                                                                                                    | E     | Inteiro     | 1-1   | 1         | 1.0    |
| 6.8.10        | IRRPF                        | Valor do Imposto de Renda Retido na Fonte                                                                                                                                             | E     | Decimal     | 1-1   | 10,2      | 1.0    |
| 6.8.11        | INSS                         | Valor do Instituto Nacional do Seguro Social                                                                                                                                          | E     | Decimal     | 1-1   | 10,2      | 1.0    |
| 6.8.12        | SESTSENAT                    | Valor do Serviço Social do Transporte e o Serviço Nacional deAprendizagem do Transporte                                                                                               | E     | Decimal     | 1-1   | 10,2      | 1.0    |
| 6.8.13        | MotivoBloqueio               | Motivo para bloqueio caso o evento seja bloqueado                                                                                                                                     | E     | Caractere   | 1-0   | 100       | 1.0    |
| 6.8.14        | ViagemDocumentos             | Array de Objetos com os dados dos documentos que compõe a parcela da viagem                                                                                                           | A     |             | 1-0   |           | 1.0    |
| ********      | Descricao                    | Descrição do documento                                                                                                                                                                | E     | Caractere   | 1-0   | 100       | 1.0    |
| ********      | TipoDocumento                | Enumeração do tipo do documento, podendo ser: <br/>__0:__ Outros;<br/>__1:__ CT-e;<br/>__2:__ NF Serviço;<br/>__3:__ Ordem de coleta;<br/>__4:__ Nota Fiscal.                         | E     | Inteiro     | 1-1   | 1         | 1.0    |
| ********      | NumeroDocumento              | Número do documento                                                                                                                                                                   | E     | Inteiro     | 1-1   | 10        | 1.0    |
| ********      | ObrigaAnexo                  | Obrigar Anexo                                                                                                                                                                         | E     | Booleano    | 1-0   | True/False| 1.0    |
| ********      | ObrigaAnexoMatriz            | Obrigar Anexo Matriz                                                                                                                                                                  | E     | Booleano    | 1-0   | True/False| 1.0    |
| 6.8.14.6      | ObrigaAnexoFilial            | Obrigar Anexo Filial                                                                                                                                                                  | E     | Booleano    | 1-0   | True/False| 1.0    |
| 6.8.14.7      | ObrigaDocOriginal            | Obrigar documento original                                                                                                                                                            | E     | Booleano    | 1-0   | True/False| 1.0    |
| 6.8.15        | ViagemOutrosDescontos        | Array de Objetos com os dados de descontos que compõe a parcela da viagem                                                                                                             | A     |             | 1-0   |           | 1.0    |
| 6.8.15.1      | Descricao                    | Descrição do documento                                                                                                                                                                | E     | Caractere   | 1-0   | 100       | 1.0    |
| 6.8.15.2      | NumeroDocumento              | Número do documento                                                                                                                                                                   | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 6.8.15.3      | Valor                        | Valor do desconto                                                                                                                                                                     | E     | Decimal     | 1-1   | 10,2      | 1.0    |
| 6.8.15.4      | CodigoERP                    | Código do ERP                                                                                                                                                                         | E     | Long        | 1-0   | 20        | 1.0    |
| 6.8.16        | ViagemOutrosAcrescimos       | Array de Objetos com os dados de acréscimos que compõe a parcela da viagem                                                                                                            | A     |             | 1-0   |           | 1.0    |
| 6.8.16.1      | Descricao                    | Descrição do documento                                                                                                                                                                | E     | Caractere   | 1-0   | 100       | 1.0    |
| 6.8.16.2      | NumeroDocumento              | Número do documento                                                                                                                                                                   | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 6.8.16.3      | Valor                        | Valor do desconto                                                                                                                                                                     | E     | Decimal     | 1-1   | 10,2      | 1.0    |
| 6.8.16.4      | CodigoERP                    | Código do ERP                                                                                                                                                                         | E     | Long        | 1-0   | 20        | 1.0    |
| 6.9           | DocumentosFiscais            | Array de Objetos com os dados dos documentos que compõe a viagem                                                                                                                      | A     |             | 1-0   |           | 1.0    |
| 6.9.1         | NumeroDocumento              | Número do documento                                                                                                                                                                   | E     | Decimal     | 1-1   | 10        | 1.0    |
| 6.9.2         | Serie                        | Série                                                                                                                                                                                 | E     | Caractere   | 1-1   | 4         | 1.0    |
| 6.9.3         | PesoSaida                    | Peso de saída                                                                                                                                                                         | E     | Decimal     | 1-1   | 10,3      | 1.0    |
| 6.9.4         | Valor                        | Valor                                                                                                                                                                                 | E     | Decimal     | 1-0   | 10,2      | 1.0    |
| 6.9.5         | TipoDocumento                | Enumeração do tipo do documento, podendo ser: <br/>__0:__ Outros;<br/>__1:__ CT-e;<br/>__2:__ NF Serviço;<br/>__3:__ Ordem de coleta;<br/>__4:__ Nota Fiscal.                         | E     | Inteiro     | 1-1   | 1         | 1.0    |
| 6.10          | AutorizacaoEstabelecimentos  | Array de Objetos com os dados dos estabelecimentos autorizados a pagar determinada parcela da viagem                                                                                  | A     |             | 1-0   |           | 1.0    |
| 6.10.1        | Cnpj                         | CNPJ do estabelecimento                                                                                                                                                               | E     | Caractere   | 1-1   | 14        | 1.0    |
| 6.10.2        | TipoEvento                   | Enumeração do tipo de parcela, podendo ser: <br/>__0:__ Adiantamento;<br/>__1:__ Saldo;<br/>__2:__ Estadia;<br/>__3:__ RPA;<br/>__4:__ Tarifa ANTT;<br/>__5:__ Abastecimento;<br/>__6:__ Abono;     | E     | Inteiro     | 1-1   | 1         | 1.0    |     
| 6.11          | Pedagio                      | Objeto com os dados para o processo de compra de pedágio para viagem                                                                                                                  | G     |             | 1-0   |           | 1.0    |
| 6.11.1        | Fornecedor                   | Enumeração com o fornecedor do pedágio, podendo ser: <br/>__0:__ Desabilitado (padrão);<br/>__1:__ Moedeiro: Carga no cartão moedeiro Extratta (__Descontinuado pela ANTT__);<br/>__2:__ Via Fácil (Sem Parar); <br/>__3:__ Move Mais;<br/>__4:__ Veloe; <br/>__5:__ Tag Extratta;  <br/>__6:__ ConectCar; <br/>__7:__ Taggy Edenred.    | E     | Inteiro     | 1-1   | 1         | 1.0    |
| 6.11.2        | TipoVeiculo                  | Enumeração com o tipo de veículo, podendo ser: <br/>__0:__ Carro;<br/>__1:__ Motocicleta;<br/>__2:__ Ônibus;<br/>__3:__ Caminhão.                                                     | E     | Inteiro     | 1-1   | 1         | 1.0    |
| 6.11.3        | QtdEixos                     | Quantidade de eixos do veículo ( somatória dos eixos de todos veículos que compõe a viagem )                                                                                          | E     | Inteiro     | 1-1   | 2         | 1.0    |
| 6.11.4        | Localizacoes                 | Array de Objetos com os dados das localizações da rota                                                                                                                                | A     |             | 1-0   |           | 1.0    |
| ********      | IbgeCidade                   | Código de IBGE da cidade                                                                                                                                                              | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 6.11.4.2      | Latitude                     | Latitude                                                                                                                                                                              | E     | Decimal     | 1-0   | 10,7      | 1.0    |
| 6.11.4.3      | Longitude                    | Longitude                                                                                                                                                                             | E     | Decimal     | 1-0   | 10,7      | 1.0    |
| 6.11.5        | ValorPedagio                 | Valor do pedágio <br/> Enviar OU identificador OU ValorPedágio OU NomeRota OU IdRotaModelo                                                                                            | E     | Decimal     | 1-0   | 10,2      | 1.0    |
| 6.11.6        | IdentificadorHistorico       | Código de identificação de rota de pedágio obtivo via métodode roteirização (calcular custo do pedágio) <br/> Se operar com rota fixa (cadastrada) deverá realizar a consulta custo rota pelo IDRotaModelo ou NomeRota e informar o identificador histórico (hash) na viagem. | E     | Guid       | 1-0   |           | 1.0    |
| 6.12          | CadastrosPreViagem           | Objeto pai com os dados para o processo de cadastro das informações necessárias para criar uma viagem                                                                                 | G     |             | 1-0   |           | 1.0    | 
| 6.12.1        | ClienteOrigem                | Objeto com os dados para a integração de um cliente do tipo origem                                                                                                                    | G     |             | 1-0   |           | 1.0    |
| ********      | BACENPais                    | Código do BACEN do país deste cliente.                                                                                                                                                | E     | Inteiro     | 1-1   | 14        | 1.0    |
| ********      | IBGEEstado                   | Código IBGE do estado.                                                                                                                                                                | E     | Inteiro     | 1-1   | 10        | 1.0    |
| ********      | IBGECidade                   | Código IBGE da cidade.                                                                                                                                                                | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 6.12.1.4      | RazaoSocial                  | Razão social do cliente.                                                                                                                                                              | E     | Caractere   | 1-1   | 100       | 1.0    |
| ********      | NomeFantasia                 | Nome fantasia do cliente.                                                                                                                                                             | E     | Caractere   | 1-1   | 100       | 1.0    |
| ********      | TipoPessoa                   | Tipo de Pessoa. 1 = Física, 2 = Jurídica                                                                                                                                              | E     | Inteiro     | 1-1   | 10        | 1.0    |
| ********      | CNPJCPF                      | CPF ou CNPJ                                                                                                                                                                           | E     | Caractere   | 1-1   | 14        | 1.0    |
| ********      | RG                           | Registro Geral / Numero do Documento de Identidade                                                                                                                                    | E     | Caractere   | 1-0   | 100       | 1.0    |
| ********      | OrgaoExpedidorRG             | Órgão Expedidor                                                                                                                                                                       | E     | Caractere   | 1-0   | 5         | 1.0    |
| ********0     | IE                           | Inscrição Estadual                                                                                                                                                                    | E     | Numerico    | 1-0   | 15        | 1.0    |
| ********1     | Celular                      | Número de celular, apenas números                                                                                                                                                     | E     | Caractere   | 1-0   | 10        | 1.0    |
| ********2     | Email                        | E-mail                                                                                                                                                                                | E     | Caractere   | 1-0   | 100       | 1.0    |
| ********3     | CEP                          | CEP do Cliente                                                                                                                                                                        | E     | Caractere   | 1-1   | 10        | 1.0    |
| ********4     | Endereco                     | Endereço                                                                                                                                                                              | E     | Caractere   | 1-1   | 100       | 1.0    |
| ********5     | Complemento                  | Complemento                                                                                                                                                                           | E     | Caractere   | 1-0   | 100       | 1.0    |
| ********6     | Numero                       | Número                                                                                                                                                                                | E     | Inteiro     | 1-0   | 10        | 1.0    |
| ********7     | Bairro                       | Bairro                                                                                                                                                                                | E     | Caractere   | 1-1   | 100       | 1.0    |
| 6.12.2        | ClienteDestino               | Objeto com os dados para a integração de um cliente do tipo destino                                                                                                                   | G     |             | 1-0   |           | 1.0    |
| ********      | BACENPais                    | Código do BACEN do país deste cliente.                                                                                                                                                | E     | Inteiro     | 1-1   | 14        | 1.0    |
| ********      | IBGEEstado                   | Código IBGE do estado.                                                                                                                                                                | E     | Inteiro     | 1-1   | 10        | 1.0    |
| ********      | IBGECidade                   | Código IBGE da cidade.                                                                                                                                                                | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 6.12.2.4      | RazaoSocial                  | Razão social do cliente.                                                                                                                                                              | E     | Caractere   | 1-1   | 100       | 1.0    |
| ********      | NomeFantasia                 | Nome fantasia do cliente.                                                                                                                                                             | E     | Caractere   | 1-1   | 100       | 1.0    |
| ********      | TipoPessoa                   | Tipo de Pessoa. 1 = Física, 2 = Jurídica                                                                                                                                              | E     | Inteiro     | 1-1   | 10        | 1.0    |
| ********      | CNPJCPF                      | CPF ou CNPJ                                                                                                                                                                           | E     | Caractere   | 1-1   | 14        | 1.0    |
| ********      | RG                           | Registro Geral / Numero do Documento de Identidade                                                                                                                                    | E     | Caractere   | 1-0   | 100       | 1.0    |
| ********      | OrgaoExpedidorRG             | Órgão Expedidor                                                                                                                                                                       | E     | Caractere   | 1-0   | 5         | 1.0    |
| ********0     | IE                           | Inscrição Estadual                                                                                                                                                                    | E     | Numerico    | 1-0   | 15        | 1.0    |
| ********1     | Celular                      | Número de celular, apenas números                                                                                                                                                     | E     | Caractere   | 1-0   | 10        | 1.0    |
| ********2     | Email                        | E-mail                                                                                                                                                                                | E     | Caractere   | 1-0   | 100       | 1.0    |
| ********3     | CEP                          | CEP do Cliente                                                                                                                                                                        | E     | Caractere   | 1-1   | 10        | 1.0    |
| ********4     | Endereco                     | Endereço                                                                                                                                                                              | E     | Caractere   | 1-1   | 100       | 1.0    |
| ********5     | Complemento                  | Complemento                                                                                                                                                                           | E     | Caractere   | 1-0   | 100       | 1.0    |
| ********6     | Numero                       | Número                                                                                                                                                                                | E     | Inteiro     | 1-0   | 10        | 1.0    |
| ********7     | Bairro                       | Bairro                                                                                                                                                                                | E     | Caractere   | 1-1   | 100       | 1.0    |
| 6.12.3        | Proprietário                 | Objeto com os dados para a integração de um proprietário                                                                                                                              | G     |             | 1-0   |           | 1.0    |
| 6.12.3.1      | CnpjCpf                      | CPF ou CNPJ do proprietário                                                                                                                                                           | E     | Caractere   | 1-1   | 14        | 1.0    |
| 6.12.3.2      | RazaoSocial                  | Razão social                                                                                                                                                                          | E     | Caractere   | 1-1   | 100       | 1.0    |
| 6.12.3.3      | NomeFantasia                 | Nome fantasia                                                                                                                                                                         | E     | Caractere   | 1-1   | 100       | 1.0    |
| 6.12.3.4      | RG                           | Registro Geral/Número da Carteira de Identidade para pessoa física                                                                                                                    | E     | Caractere   | 1-0   | 50        | 1.0    |
| 6.12.3.5      | RGOrgaoExpedidor             | Órgão expedidor para pessoa física                                                                                                                                                    | E     | Caractere   | 1-0   | 10        | 1.0    |
| 6.12.3.6      | RNTRC                        | Registro nacional de transportadores de carga                                                                                                                                         | E     | Inteiro     | 1-1   | 10        | 1.0    |
| ********      | IE                           | Inscrição Estadual para pessoa jurídica                                                                                                                                               | E     | Inteiro     | 1-0   | 15        | 1.0    |
| ********      | TipoContrato                 | Tipo de contrato do proprietário a ser integrado<br/>__1:__ Frota,<br/>__2:__ Cooperado,<br/>__3:__ Agregado/Terceiro)                                                                | E     | Inteiro     | 1-1   | 1         | 1.0    |
| ********      | DataNascimento               | Data de nascimento do proprietário pessoa física                                                                                                                                      | E     | Data        | 1-1   | yyyy-MM-dd| 1.0    |
| *********     | Cartao                       | Objeto com os dados para a vinculação de um cartão a um proprietário                                                                                                                  | G     |             | 1-0   |           | 1.0    |
| *********.1   | NumeroCartao                 | Número de identificação do cartão                                                                                                                                                     | E     | Inteiro     | 1-1   | 10        | 1.0    |
| *********.2   | RealizarTrocaCartao          | Flag para indicar se deve ser realizado a troca do cartão caso o proprietário já tenha um cartão vinculado                                                                            | E     | Booleano    | 1-1   | True/False| 1.0    |                                                                                              | E     | Caractere   | 1-1   | 100       | 1.0    |
| 6.12.3.11     | Contatos                     | Array de Objetos com os dados de contato de um proprietário                                                                                                                           | A     |             | 1-1   |           | 1.0    |
| 6.12.3.11.1   | Email                        | E-mail do proprietário                                                                                                                                                                | E     | Caractere   | 1-0   | 20        | 1.0    |
| 6.12.3.11.2   | Telefone                     | Telefone do proprietário                                                                                                                                                              | E     | Caractere   | 1-1   | 20        | 1.0    |
| 6.12.3.11.3   | Celular                      | Celular do proprietário                                                                                                                                                               | E     | Inteiro     | 1-0   | 10        | 1.0    |
| 6.12.3.12     | Enderecos                    | Array de Objetos com os dados de endereço de um proprietário                                                                                                                          | A     |             | 1-0   |           | 1.0    |
| 6.12.3.12.1   | CEP                          | CEP                                                                                                                                                                                   | E     | Caractere   | 1-1   | 8         | 1.0    |
| 6.12.3.12.2   | Endereco                     | Endereço do proprietário                                                                                                                                                              | E     | Caractere   | 1-1   | 100       | 1.0    |
| 6.12.3.12.3   | Complemento                  | Complemento                                                                                                                                                                           | E     | Caractere   | 1-0   | 100       | 1.0    |
| 6.12.3.12.4   | Numero                       | Número                                                                                                                                                                                | E     | Inteiro     | 1-0   | 10        | 1.0    |
| 6.12.3.12.5   | Bairro                       | Bairro                                                                                                                                                                                | E     | Caractere   | 1-1   | 100       | 1.0    |
| 6.12.3.12.6   | IBGECidade                   | Código IBGE da cidade                                                                                                                                                                 | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 6.12.3.12.7   | IBGEEstado                   | Código IBGE do estado                                                                                                                                                                 | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 6.12.3.12.8   | BACENPais                    | Código BACEN do país                                                                                                                                                                  | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 6.12.4        | Motorista                    | Objeto com os dados para a integração de um motorista                                                                                                                                 | G     |             | 1-0   |           | 1.0    |
| 6.12.4.1      | Nome                         | Nome do motorista                                                                                                                                                                     | E     | Caractere   | 1-1   | 100       | 1.0    |
| 6.12.4.2      | RG                           | Registro geral/Documento de Identidade                                                                                                                                                | E     | Caractere   | 1-1   | 100       | 1.0    |
| 6.12.4.3      | RGOrgaoExpedidor             | Órgão de expedição do registro geral                                                                                                                                                  | E     | Caractere   | 1-1   | 10        | 1.0    |
| 6.12.4.4      | CPF                          | CPF do motorista                                                                                                                                                                      | E     | Caractere   | 1-1   | 11        | 1.0    |
| 6.12.4.5      | Sexo                         | Sexo: M ou F (Masculino/Feminino)                                                                                                                                                     | E     | Caractere   | 1-1   | 1         | 1.0    |
| 6.12.4.6      | CNH                          | Carteira nacional de habilitação / Número do Registro da CNH                                                                                                                          | E     | Caractere   | 1-1   | 11        | 1.0    |
| 6.12.4.7      | CNHCategoria                 | Categoria da carteira nacional de habilitação: A, B, AB, C, D ou E                                                                                                                    | E     | Caractere   | 1-1   | 2         | 1.0    |
| 6.12.4.8      | ValidadeCNH                  | Validade da CNH do motorista                                                                                                                                                          | E     | Data        | 1-1   | yyyy-MM-dd| 1.0    |
| ********      | Celular                      | Número de contato com DDD                                                                                                                                                             | E     | Caractere   | 1-0   | 11        | 1.0    |
| *********     | TipoContrato                 | Tipo do contrato: <br/>__1:__ Frota, <br/>__2:__ Cooperado,<br/>__3:__ Agregado,<br/>__4:__ Terceiro                                                                                  | E     | Inteiro     | 1-1   | 10        | 1.0    |
| *********     | Email                        | E-mail de contato                                                                                                                                                                     | E     | Caractere   | 1-0   | 100       | 1.0    |
| *********     | IBGECidade                   | Código IBGE da cidade                                                                                                                                                                 | E     | Inteiro     | 1-1   | 10        | 1.0    |
| *********     | IBGEEstado                   | Código IBGE do estado                                                                                                                                                                 | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 6.12.4.14     | BACENPais                    | Código BACEN do país                                                                                                                                                                  | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 6.12.4.15     | CEP                          | CEP do endereço                                                                                                                                                                       | E     | Caractere   | 1-1   | 100       | 1.0    |
| 6.12.4.16     | Endereco                     | Endereço do motorista                                                                                                                                                                 | E     | Caractere   | 1-1   | 100       | 1.0    |
| 6.12.4.17     | Complemento                  | Complemento                                                                                                                                                                           | E     | Caractere   | 1-0   | 100       | 1.0    |
| 6.12.4.18     | Numero                       | Número da residência                                                                                                                                                                  | E     | Caractere   | 1-0   | 5         | 1.0    |
| 6.12.4.19     | Bairro                       | Bairro                                                                                                                                                                                | E     | Caractere   | 1-1   | 50        | 1.0    |
| 6.12.4.20     | FormularioCnh                | Formulário CNH                                                                                                                                                                        | E     | Caractere   | 1-0   | 100       | 1.0    |
| 6.12.4.21     | Cartao                       | Objeto com os dados para a vinculação de um cartão a um motorista                                                                                                                     | G     |             | 1-0   |           | 1.0    |
| 6.12.4.21.1   | NumeroCartao                 | Número de identificação do cartão                                                                                                                                                     | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 6.12.4.21.2   | RealizarTrocaCartao          | Flag para indicar se deve ser realizado a troca do cartão caso o proprietário já tenha um cartão vinculado                                                                            | E     | Booleano    | 1-1   | True/False| 1.0    |
| 6.12.5        | Veículo                      | Objeto com os dados para a integração de um veículo                                                                                                                                   | G     |             | 1-0   |           | 1.0    |
| 6.12.5.1      | CPFCNPJProprietario          | CPF ou CNPJ do proprietário do veículo, associado a ANTT                                                                                                                              | E     | Caractere   | 1-0   | 14        | 1.0    |
| 6.12.5.2      | CPFMotorista                 | CPF do motorista                                                                                                                                                                      | E     | Caractere   | 1-0   | 11        | 1.0    |
| 6.12.5.3      | Placa                        | Placa do veículo                                                                                                                                                                      | E     | Caractere   | 1-1   | 7         | 2.0    |
| 6.12.5.4      | Chassi                       | Número do chassi                                                                                                                                                                      | E     | Caractere   | 1-1   | 22        | 1.0    |
| 6.12.5.5      | RENAVAM                      | Número do RENAVAM                                                                                                                                                                     | E     | Inteiro     | 1-1   | 11        | 1.0    |
| 6.12.5.6      | AnoFabricacao                | Ano de fabricação do veículo                                                                                                                                                          | E     | Inteiro     | 1-0   | 10        | 1.0    |
| 6.12.5.7      | AnoModelo                    | Ano do modelo                                                                                                                                                                         | E     | Inteiro     | 1-0   | 10        | 1.0    |
| 6.12.5.8      | Marca                        | Marca do Veículo                                                                                                                                                                      | E     | Caractere   | 1-1   | 50        | 1.0    |
| 6.12.5.9      | Modelo                       | Modelo do Veículo                                                                                                                                                                     | E     | Caractere   | 1-1   | 50        | 1.0    |
| *********     | ComTracao                    | Se possui tração: <br/>__true - Sim:__ Cavalo/Utilitário <br/>__false - Não:__ Carreta                                                                                                | E     | Booleano    | 1-1   | True/False| 1.0    |
| *********     | TipoRodagem                  | Enum de tipo de rodagem, podendo ser eles: <br/> __1 -__ Simples; <br/> __2 -__ Duplo.                                                                                                | E     | Inteiro     | 1-1   | 1         | 1.0    |
| *********     | IdTipoCarreta                | Id do tipo de carreta                                                                                                                                                                 | E     | Inteiro     | 1-0   | 10        | 1.0    |
| *********     | TipoContrato                 | Tipo de contrato do veículo, podendo ser eles: <br/> __1 -__ Frota; <br/> __2 -__ Cooperado;  <br/> __3 -__ Agregado; <br/> __4 -__ Terceiro.                                         | E     | Inteiro     | 1-1   | 1         | 1.0    |
| *********     | QuantidadeEixos              | Quantidade de eixos do veículo. <br/>__IMPORTANTE__ enviar a quantidade correta de eixos, pois o calculo de pedágio é baseado nesta quantidade.                                       | E     | Inteiro     | 1-1   | 10        | 1.0    |
| *********     | IdTipoCavalo                 | Id do tipo de cavalo                                                                                                                                                                  | E     | Inteiro     | 1-0   | 10        | 1.0    |
| *********     | IBGECidade                   | Código IBGE da cidade                                                                                                                                                                 | E     | Inteiro     | 1-0   | 10        | 1.0    |
| 6.12.6        | Carretas                     | Objeto com os dados para a integração de um veículo                                                                                                                                   | G     |             | 1-0   |           | 1.0    |     
| ********      | CPFCNPJProprietario          | CPF ou CNPJ do proprietário                                                                                                                                                           | E     | Caractere   | 1-0   | 14        | 1.0    |
| 6.12.6.2      | Placa                        | Placa do veículo                                                                                                                                                                      | E     | Caractere   | 1-1   | 7         | 2.0    |
| 6.12.6.3      | RNTRC                        | Registro Nacional Transporte Rodoviário de Carga ( ANTT )                                                                                                                             | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 6.12.6.4      | Chassi                       | Número do chassi                                                                                                                                                                      | E     | Caractere   | 1-1   | 22        | 1.0    |
| 6.12.6.5      | RENAVAM                      | Número do RENAVAM                                                                                                                                                                     | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 6.12.6.6      | AnoFabricacao                | Ano de fabricação do veículo                                                                                                                                                          | E     | Inteiro     | 1-0   | 10        | 1.0    |
| 6.12.6.7      | AnoModelo                    | Ano do modelo                                                                                                                                                                         | E     | Inteiro     | 1-0   | 10        | 1.0    |
| 6.12.6.8      | Marca                        | Marca do Veículo                                                                                                                                                                      | E     | Caractere   | 1-1   | 50        | 1.0    |
| 6.12.6.9      | Modelo                       | Modelo do Veículo                                                                                                                                                                     | E     | Caractere   | 1-1   | 50        | 1.0    |
| ********0     | ComTracao                    | Se possui tração: <br/>__true - Sim:__ Cavalo/Utilitário <br/>__false - Não:__ Carreta                                                                                                | E     | Booleano    | 1-1   | True/False| 1.0    |
| ********1     | TipoRodagem                  | Enum de tipo de rodagem, podendo ser eles: <br/> __1 -__ Simples; <br/> __2 -__ Duplo.                                                                                                | E     | Inteiro     | 1-1   | 1         | 1.0    |
| ********2     | IdTipoCarreta                | Id do tipo de carreta                                                                                                                                                                 | E     | Inteiro     | 1-0   | 10        | 1.0    |
| ********3     | TipoContrato                 | Tipo de contrato do veículo, podendo ser eles: <br/> __1 -__ Frota; <br/> __2 -__ Cooperado;  <br/> __3 -__ Agregado; <br/> __4 -__ Terceiro.                                         | E     | Inteiro     | 1-1   | 1         | 1.0    |
| ********4     | QuantidadeEixos              | Quantidade de eixos do veículo. <br/>__IMPORTANTE__ enviar a quantidade correta de eixos, pois o calculo de pedágio é baseado nesta quantidade.                                       | E     | Inteiro     | 1-1   | 10        | 1.0    |
| ********5     | IdTipoCavalo                 | Id do tipo de cavalo                                                                                                                                                                  | E     | Inteiro     | 1-0   | 10        | 1.0    |
| ********6     | IBGECidade                   | Código IBGE da cidade                                                                                                                                                                 | E     | Inteiro     | 1-0   | 10        | 1.0    |
| ********7     | Municipio                    | Municipio do emplacamento                                                                                                                                                             | E     | Caractere   | 1-1   | 50        | 1.0    |
| ********8     | NumeroFrota                  | Numero da Frota                                                                                                                                                                       | E     | Long        | 1-0   | 20        | 1.0    |
| ********9     | CNPJFilial                   | CNPJ da Filial                                                                                                                                                                        | E     | Caractere   | 1-0   | 14        | 1.0    |
| *********     | CorVeiculo                   | Cor do Veiculo                                                                                                                                                                        | E     | Caractere   | 1-1   | 50        | 1.0    |

<br>

## <b>Modelo de Json - Request</b>

<br>

```json
{
  "Token": "",
  "CnpjAplicacao": "",
  "CNPJEmpresa": "",
  "DocumentoUsuarioAudit": "",
  "NomeUsuarioAudit": "",
  "DadosViagem": {
    "DadosIniciais": {
      "NumeroControle": "",
      "DeclaracaoCiot": 0,
      "Produto": "",
      "UnidadeMedidaProduto": 0,
      "NaturezaCarga": 0,
      "RealizarIntegracoesPreViagem": true,
      "GerarCiotTacAgregado": true,
      "RealizarIntegracoesPreViagem": true
    },
    "Documentos": {
      "ClienteOrigemDocumento": "",
      "ClienteDestinoDocumento": "",
      "ClienteTomadorDocumento": "",
      "FilialDocumento": ""
      "MotoristaDocumento": "",
      "ProprietarioDocumento": "",
      "NumeroDocumentoNotaCliente": ""
    },
    "Valores": {
      "Irrpf": ,
      "Inss": ,
      "SestSenat": ,
      "PesoSaida": ,
      "ValorMercadoria": ,
      "Quantidade": 
    },
    "Enderecos": {
      "EnderecoColeta": "",
      "EnderecoEntrega": ""
    },
    "DadosPagamento": {
       "FormaPagamento": 0,
       "CodigoBacen": "",
       "Agencia": 0,
       "Conta": "",
       "TipoConta": 0,

    },
    "Veiculo": {
      "Rntrc": "",
      "Placa": ""
    },
    "Datas": {
      "DataColeta": "2024-12-31 23:59:59",
      "DataPrevisaoEntrega": "2024-12-31 23:59:59",
      "DataEmissaoDocumentoFiscal": "2024-12-31 23:59:59"
    },
    "ViagemEventos": {
       "IdViagemEvento": null,
       "HabilitarPagamentoCartao": true,
       "NumeroControle": "",
       "TipoEvento": 1,
       "ValorPagamento": 0.0,
       "DataValidade": "2024-12-31",
       "NumeroRecibo": "",
       "Instrucao": "",
       "Status": 0,
       "DataAgendamentoPagamento": "2024-12-31",
       "IRRPF": 0.0,
       "INSS": 0.0,
       "SESTSENAT": 0.0,
       "MotivoBloqueio": "",
       "DadosAbastecimento": {
         "CodigoCredito": "",
         "CodigoCliente": "",
         "CodigoProduto": "",
         "numerocartao": "",
         "DataValidade": "2024-12-31",
         "DataLiberacao": "2024-12-31",
         "Fornecedor": 0
       },
       "ViagemDocumentos": [
         {
            "Descricao": "",
            "TipoDocumento": 1,
            "NumeroDocumento": 0,
            "ObrigaAnexo": true,
            "ObrigaAnexoMatriz": false,
            "ObrigaAnexoFilial": false,
            "ObrigaDocOriginal": false
         }
       ],
       "ViagemOutrosDescontos": [
         {
            "Descricao": "",
            "NumeroDocumento": 0,
            "Valor": 0.0,
            "CodigoERP": 0,
         }
       ]
    },
    "DocumentosFiscais": [
      {
        "NumeroDocumento": 0,
        "Serie": "",
        "PesoSaida": 0.0,
        "Valor": 0.0,
        "TipoDocumento": 1
      }
    ],
    "AutorizacaoEstabelecimentos": {
        "Cnpj": "",
        "TipoEvento": 0
    }
    "Pedagio": {
      "Fornecedor": 0,
      "TipoVeiculo": 0,
      "QtdEixos": 0,
      "Localizacoes": [
        "IbgeCidade": 0,
        "Latitude": 0.0,
        "Longitude": 0.0,
      ],
      "ValorPedagio": 0.0,
      "IdentificadorHistorico": ""
    },
    "CadastrosPreViagem": {
      "ClienteOrigem": {
        "BACENPais": 0,
        "IBGEEstado": 0,
        "IBGECidade": 0,
        "RazaoSocial": "",
        "NomeFantasia": "",
        "TipoPessoa": 1,
        "CNPJCPF": "",
        "RG": "",
        "OrgaoExpedidorRG": "",
        "IE": 0,
        "Celular": "",
        "Email": "",
        "CEP": "",
        "Endereco": "",
        "Complemento": "",
        "Numero": 0,
        "Bairro": ""
      },
      "ClienteDestino": {
       "BACENPais": 0,
        "IBGEEstado": 0,
        "IBGECidade": 0,
        "RazaoSocial": "",
        "NomeFantasia": "",
        "TipoPessoa": 1,
        "CNPJCPF": "",
        "RG": "",
        "OrgaoExpedidorRG": "",
        "IE": 0,
        "Celular": "",
        "Email": "",
        "CEP": "",
        "Endereco": "",
        "Complemento": "",
        "Numero": 0,
        "Bairro": ""
      },
      "Proprietario": {
        "CnpjCpf": "",
        "RazaoSocial": "",
        "NomeFantasia": "",
        "RG": "",
        "RGOrgaoExpedidor": "",
        "RNTRC": "",
        "IE": 0,
        "TipoContrato": 1,
        "DataNascimento": "",
        "Cartao": {
            "NumeroCartao": 0,
            "RealizarTrocaCartao": false,
        }
        "Contatos": [
          {
            "Email": "",
            "Telefone": "",
            "Celular": ""
          }
        ],
        "Enderecos": [
          {
            "CEP": "",
            "Endereco": "",
            "Complemento": "",
            "Numero": 0,
            "Bairro": "",
            "IBGECidade": 0,
            "IBGEEstado": 0,
            "BACENPais": 0 
          }
        ]
      },
      "Motorista": {
        "Nome": "",
        "RG": "",
        "RGOrgaoExpedidor": "",
        "CPF": "",
        "Sexo": "",
        "CNH": "",
        "CNHCategoria": "",
        "ValidadeCNH": "",
        "Celular": "",
        "TipoContrato": 0,
        "Email": "",
        "IBGECidade": 0,
        "IBGEEstado": 0,
        "BACENPais": 0,
        "CEP": "",
        "Endereco": "",
        "Complemento": "",
        "Numero": 0,
        "Bairro": "",
        "FormularioCnh": "",
        "Cartao": {
            "NumeroCartao": 0,
            "RealizarTrocaCartao": 
        }
      },
      "Veiculo": {
        "CPFCNPJProprietario": "",
        "CPFMotorista": "",
        "Placa": "",
        "Chassi": "",
        "RENAVAM": 0,
        "AnoFabricacao": "2024-12-31",
        "AnoModelo": "2024-12-31",
        "Marca": "",
        "Modelo": "",
        "ComTracao": true,
        "TipoRodagem": 1,
        "IdTipoCarreta": 0,
        "TipoContrato": 1,
        "QuantidadeEixos": 0,
        "IdTipoCavalo": 0,
        "IBGECidade": 0 
      },
      "Carretas": [
        {
          "CPFCNPJProprietario": "",
          "CPFMotorista": "",
          "Placa": "",
          "Chassi": "",
          "RENAVAM": "",
          "AnoFabricacao": "2024-12-31",
          "AnoModelo": "2024-12-31",
          "Marca": "",
          "Modelo": "",
          "ComTracao": false,
          "TipoRodagem": 1,
          "IdTipoCarreta": 0,
          "TipoContrato": 1,
          "QuantidadeEixos": 0,
          "IdTipoCavalo": 0,
          "IBGECidade": 0,
          "Municipio": ""
          "NumeroFrota": 0
          "CNPJFilial": ""
          "CorVeiculo": ""
        }
      ]
    }
  }
}
```

<br>

## <b>Response</b>

<br>

| Índice        | Campo                        | Descrição                                                                                                                                                                                                                        | Elem. | Tipo        | Ocor. |   Tam.    | Versão |
|---------------|------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------|-------------|-------|-----------|--------|
| 1             | Sucesso                      | Resultado da requisição, caso não passe por uma validação de negócio ou um erro interno do sistema será 'false'                                                                                                                  | E     | Booleano    | 1-1   | True/False| 1.0    |
| 2             | Mensagem                     | Mensagem do resultado da requisição                                                                                                                                                                                              | E     | Caractere   | 1-1   | 200       | 1.0    |
| 3             | Objeto                       | Objeto pai que contém os dados                                                                                                                                                                                                   | G     |             | 0-1   |           | 1.0    |
| 3.1           | IdViagem                     | Código da viagem                                                                                                                                                                                                                 | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 3.2           | NumeroDocumento              | Número do documento                                                                                                                                                                                                              | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 3.3           | IdsViagemEstabelecimento     | Lista de ids dos estabelecimentos da viagem                                                                                                                                                                                      | A     | Inteiro     | 0-1   | 10        | 1.0    |
| 3.4           | Irrpf                        | Valor do imposto                                                                                                                                                                                                                 | E     | Decimal     | 0-1   | 10,2      | 1.0    |
| 3.5           | Inss                         | Valor do impostoo                                                                                                                                                                                                                | E     | Decimal     | 0-1   | 10,2      | 1.0    |
| 3.6           | SestSenat                    | Valor do imposto                                                                                                                                                                                                                 | E     | Decimal     | 0-1   | 10,2      | 1.0    |
| 3.7           | Eventos                      | Array de Objetos com os dados das parcelas que compõe a viagem                                                                                                                                                                   | A     |             | 1-1   |           | 1.0    |
| 3.7.1         | IdViagemEvento               | Código de identidade do evento da viagem previamente integrado, utilizado para edição de um evento                                                                                                                               | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 3.7.2         | NumeroControle               | Número de controle do evento para ERP                                                                                                                                                                                            | E     | Caractere   | 0-1   | 300       | 1.0    |
| 3.7.3         | Token                        | Token do evento da viagem                                                                                                                                                                                                        | E     | Caractere   | 1-1   | 300       | 1.0    |
| 3.7.4         | TipoEventoViagem             | Enumeração com o tipo de parcela, podendo ser: <br/>__0:__ Adiantamento;<br/>__1:__ Saldo;<br/>__2:__ Estadia;<br/>__3:__ RPA;<br/>__4:__ Tarifa ANTT;<br/>__5:__ Abastecimento;<br/>__6:__ Abono;                               | E     | Inteiro     | 1-1   | 1         | 1.0    |
| 3.7.5         | OperacaoCartao               | Array com informações referente ao status última transação do evento.                                                                                                                                                            | A     |             | 0-1   |           | 1.0    |
| *******       | Status                       | Status da última transação do evento, podendo ser carga, transferência, ou estorno. <br/>__0:__ Pendente - Falha ao realizar a carga no cartão, porém o próprio serviço do meio homologado está encarregado de reprocessar a transação. Isto pode acontecer em caso de instabilidade na comunicação com a instituição financeira. Nosso serviço mantem o operacional funcionando, e indica a situação neste status;<br/>__1:__ Sucesso - Carga realizada com sucesso;<br/>__2:__ Erro - Erro ao realizar a carga de crédito, onde a plataforma do meio homologado não irá tentar refazer o processo automaticamente;<br/>__3:__ Não habilitado - Não houve necessidade de pagamento no cartão para o evento. Isto ocorre enquanto o status do evento é aberto ou cancelado, e quando o TMS integra a viagem com a funcionalidade de cartão desabilitada;<br/>__4:__ Agendado: Evento com pagamento agendado automaticamente para uma data futura.       | E     | Inteiro     | 1-1   | 1         | 1.0    |
| 3.7.5.2       | Mensagem                     | Mensagem do status do processamento                                                                                                                                                                                              | E     | Caractere   | 1-1   | 200       | 1.0    |
| 3.7.6         | IdsViagemDocumento           | Array que contém uma lista de documentos do evento                                                                                                                                                                               | A     |             | 0-1   |           | 1.0    |
| 3.7.6.1       | IdViagemDocumento            | Código do documento da viagem informado para a edição do registro                                                                                                                                                                | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 3.7.6.2       | NumeroDocumento              | Número do documento                                                                                                                                                                                                              | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 3.7.7         | IdsViagemOutrosDescontos     | Lista de códigos de outros descontos                                                                                                                                                                                             | A     | Inteiro     | 0-1   |           | 1.0    |
| 3.7.8         | ViagemOutrosDescontos        | Objeto pai que contém uma lista de outros descontos relacionados a um evento da viagem                                                                                                                                           | A     |             | 0-1   |           | 1.0    |
| 3.7.8.1       | IdViagemOutrosDescontos      | Código do registro de outros descontos da viagem informado para a edição do registro                                                                                                                                             | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 3.7.8.2       | NumeroDocumento              | Número do documento                                                                                                                                                                                                              | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 3.7.8.3       | Descrição                    | Descrição                                                                                                                                                                                                                        | E     | Caractere   | 0-1   | 100       | 1.0    |
| 3.7.8.4       | Valor                        | Valor do desconto                                                                                                                                                                                                                | E     | Decimal     | 1-1   | 10,2      | 1.0    |
| 3.7.9         | IdsViagemOutrosAcrescimos    | Lista de códigos de outros acréscimos                                                                                                                                                                                            | A     | Inteiro     | 0-1   |           | 1.0    |
| 3.7.10        | ViagemOutrosAcrescimos       | Objeto pai que contém uma lista de outros acrescimos relacionados a um evento da viagem                                                                                                                                          | A     |             | 0-1   |           | 1.0    |
| 3.7.10.1      | IdViagemOutrosDescontos      | Código do registro de outros valores da viagem informado para a edição do registro                                                                                                                                               | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 3.7.10.2      | NumeroDocumento              | Número do documento                                                                                                                                                                                                              | E     | Inteiro     | 1-1   | 10        | 1.0    |
| 3.7.10.3      | Descrição                    | Descrição                                                                                                                                                                                                                        | E     | Caractere   | 0-1   | 100       | 1.0    |
| 3.7.10.4      | Valor                        | Valor do desconto                                                                                                                                                                                                                | E     | Decimal     | 1-1   | 10,2      | 1.0    |
| 3.8           | Ciot                         | Objeto que contém informações relacionadas ao CIOT                                                                                                                                                                               | G     |             | 0-1   |           | 1.0    |
| 3.8.1         | Resultado                    | Indica o status da declaração de CIOT em relação a obrigatoriedade de registro na ANTT:<br/>__0:__ Sucesso;<br/>__1:__ Erro;<br/>__2:__ NaoObrigatorio;<br/>__3:__ NaoHabilitado.                                                | E     | Inteiro     | 1-1   | 1         | 1.0    |
| 3.8.2         | Declarado                    | <br/>__true:__ Ciot declarado; <br/>__false:__ Ciot não declarado.                                                                                                                                                               | E     | Booleano    | 1-1   | True/False| 1.0    |
| 3.8.3         | Mensagem                     | Mensagem informativa sobre a declaração do CIOT, em casos de falhas, também é indicado a mensagem do erro.                                                                                                                       | E     | Caractere   | 0-1   | 200       | 1.0    |
| 3.8.4         | Dados                        | Objeto que contém informações com os dados integrados na ANTT referente ao CIOT                                                                                                                                                  | G     |             | 0-1   |           | 1.0    |
| 3.8.4.1       | Ciot                         | Número do CIOT gerado pelo meio homologado                                                                                                                                                                                       | E     | Caractere   | 1-1   | 12        | 1.0    |
| 3.8.4.2       | Verificador                  | Número verificador do CIOT gerado pela ANTT. Contém os 4 últimos dígitos, caso a ANTT estiver operando em modo de contingência, é gerado os caracteres “XXXX” para indicar a situação, que também é um número válido autorizado pela ANTT.  | E     | Caractere   | 1-1   | 4         | 1.0    |
| 3.8.4.3       | Senha                        | Senha de acesso ao CIOT gerada pelo meio homologado. Utilizado em páginas públicas do meio homologado.                                                                                                                           | E     | Caractere   | 1-1   | 20        | 1.0    |
| 3.8.4.4       | EmContigencia                | Indicador se a viagem foi declarada em contingência na ANTT                                                                                                                                                                      | E     | Booleano    | 1-1   | True/False| 1.0    |
| 3.8.4.5       | DataDeclaracao               | Data e hora da integração com ANTT                                                                                                                                                                                               | E     | Caractere   | 1-1   | yyyy-MM-dd HH:mm:ss | 1.0    |
| 3.8.4.6       | AvisoTransportador           | Mensagem de aviso cadastrada pela ANTT para o contratado. Sempre que existir esta mensagem, sua apresentação ao transportador e impressão no documento correspondente é obrigatória.                                             | E     | Caractere   | 0-1   | 300       | 1.0    |
| 3.9           | Pedagio                      | Objeto que contém informações relacionadas a compra do pedágio caso habilitada                                                                                                                                                   | G     |             | 0-1   |           | 1.0    |
| 3.9.1         | Status                       | Enumeração de indicador de sucesso na comunicação com fornecedor de pedágio, podendo ser: <br/>__0:__ CompraSolicitada;<br/>__1:__ Erro;<br/>__2:__ NaoRealizado;<br/>__3:__ CancelamentoSolicitado;<br/>__4:__ CompraConfirmada;<br/>__5:__ CancelamentoConfirmado.            | E     | Inteiro     | 1-1   | 1         | 1.0    |
| 3.9.1         | Mensagem                     | Mensagem com indicador da mensagem de falha do processo.                                                                                                                                                                         | E     | Caractere   | 0-1   | 200       | 1.0    |
| 3.9.1         | Valor                        | Valor solicitado para carga de pedágio                                                                                                                                                                                           | E     | Decimal     | 1-1   | 10,2      | 1.0    |
| 3.9.1         | EstornoSaldoResidualSolicitado | Indicador se foi solicitado estorno de saldo residual nesta compra                                                                                                                                                             | E     | Booleano    | 1-1   | True/False| 1.0    |
| 3.9.1         | ProtocoloValePedagio         | Número de protocolo do registro de pedágio na ANTT.                                                                                                                                                                              | E     | Caractere   | 0-1   | 14        | 1.0    |
| 3.9.1         | ProtocoloEnvioValePedagio    | Número de protocolo do registro de envio do pedágio para ANTT. <br/> __Deve ser informado no MDF-e o campo sem os 8 primeiros dígitos que identificam a Fornecedora de Vale-Pedágio obrigatório__;                               | E     | Caractere   | 0-1   | 28        | 1.0    |
| 3.9.1         | Fornecedor                   | Enumeração com o fornecedor do pedágio, podendo ser: <br/>__0:__ Desabilitado (padrão);<br/>__1:__ Moedeiro: Carga no cartão moedeiro Extratta (__Descontinuado pela ANTT__);<br/>__2:__ Via Fácil (Sem Parar); <br/>__3:__ Move Mais;<br/>__4:__ Veloe; <br/>__5:__ Tag Extratta;  <br/>__6:__ ConectCar; <br/>__7:__ Taggy Edenred.        | E     | Inteiro     | 1-1   | 1         | 1.0    |
| 3.9.1         | AvisoTransportador           | Mensagem de aviso cadastrada pela ANTT para o contratado. Sempre que existir esta mensagem, sua apresentação ao transportador e impressão no documento correspondente é obrigatória.                                             | E     | Caractere   | 0-1   | 300       | 1.0    |
| 3.10          | IntegracoesPreViagem         | Objeto que contém dados das pré integrações realizadas                                                                                                                                                                           | G     |             | 0-1   |           | 1.0    |
| 3.10.1        | ClienteOrigem                | Objeto com os dados da integração de um cliente do tipo origem                                                                                                                                                                   | G     |             | 0-1   |           | 1.0    |
| ********      | Sucesso                      | Resultado da requisição, caso não passe por uma validação de negócio ou um erro interno do sistema será 'false'                                                                                                                  | E     | Booleano    | 1-1   | True/False| 1.0    |
| ********      | Mensagem                     | Mensagem do resultado da requisição                                                                                                                                                                                              | E     | Caractere   | 0-1   | 200       | 1.0    |
| 3.10.2        | ClienteDestino               | Objeto com os dados da integração de um cliente do tipo destino                                                                                                                                                                  | G     |             | 0-1   |           | 1.0    |
| ********      | Sucesso                      | Resultado da requisição, caso não passe por uma validação de negócio ou um erro interno do sistema será 'false'                                                                                                                  | E     | Booleano    | 1-1   | True/False| 1.0    |
| ********      | Mensagem                     | Mensagem do resultado da requisição                                                                                                                                                                                              | E     | Caractere   | 0-1   | 200       | 1.0    |
| 3.10.3        | Proprietario                 | Objeto com os dados da integração do proprietário                                                                                                                                                                                | G     |             | 0-1   |           | 1.0    |
| 3.10.3.1      | Sucesso                      | Resultado da requisição, caso não passe por uma validação de negócio ou um erro interno do sistema será 'false'                                                                                                                  | E     | Booleano    | 1-1   | True/False| 1.0    |
| 3.10.3.2      | Mensagem                     | Mensagem do resultado da requisição                                                                                                                                                                                              | E     | Caractere   | 0-1   | 200       | 1.0    |
| 3.10.4        | Motorista                    | Objeto com os dados da integração do motorista                                                                                                                                                                                   | G     |             | 0-1   |           | 1.0    |
| 3.10.4.1      | Sucesso                      | Resultado da requisição, caso não passe por uma validação de negócio ou um erro interno do sistema será 'false'                                                                                                                  | E     | Booleano    | 1-1   | True/False| 1.0    |
| 3.10.4.2      | Mensagem                     | Mensagem do resultado da requisição                                                                                                                                                                                              | E     | Caractere   | 0-1   | 200       | 1.0    |
| 3.10.5        | Veiculo                      | Objeto com os dados da integração do veículo                                                                                                                                                                                     | G     |             | 0-1   |           | 1.0    |
| 3.10.5.1      | Sucesso                      | Resultado da requisição, caso não passe por uma validação de negócio ou um erro interno do sistema será 'false'                                                                                                                  | E     | Booleano    | 1-1   | True/False| 1.0    |
| 3.10.5.2      | Mensagem                     | Mensagem do resultado da requisição                                                                                                                                                                                              | E     | Caractere   | 0-1   | 200       | 1.0    |
| 4             | Faults                       | Objeto pai que contém uma lista de erro(s) ou aviso(s) caso aconteça                                                                                                                                                             | A     |           |         |           |        |
| 4.1           | Type                         | Tipo de erro ou aviso, podendo ser eles: <br/> __1 -__ Error; <br/> __2 -__ Alert.                                                                                                                                               | E     | Inteiro   | 0-1     | 1         | 1.0    |
| 4.2           | Code                         | Codigo do erro                                                                                                                                                                                                                   | E     | Caractere | 0-1     | 10        | 1.0    |
| 4.3           | Message                      | Mensagem do erro                                                                                                                                                                                                                 | E     | Caractere | 0-1     | 200       | 1.0    |

<br>

## <b>Modelo de Json - Response</b>

<br>

```json
{
  "Sucesso": true,
  "Mensagem": "",
  "Objeto": {
    "IdViagem": 0,
    "NumeroDocumento": "",
    "IdsViagemEstabelecimento": [],
    "Irrpf": 0.0,
    "Inss": 0.0,
    "SestSenat": 0.0,
    "Eventos": [
      {
        
        "IdViagemEvento": 0,
        "NumeroControle": "",
        "Token": "",
        "TipoEventoViagem": 0,
        "OperacaoCartao": {
          "Status": 0,
          "Mensagem": ""
        },
        "IdsViagemDocumento": [
          {
            "IdViagemDocumento": 0,
            "NumeroDocumento": 0
          }
        ],
        "IdsViagemOutrosDescontos": [],
        "ViagemOutrosDescontos": [
         {
            "IdViagemOutrosDescontos": 0,
            "NumeroDocumento": 0,
            "Descricao": "",
            "Valor": 0.0
          }
        ],
        "IdsViagemOutrosAcrescimos": [],
        "ViagemOutrosAcrescimos": [
        {
            "IdViagemOutrosDescontos": 0, 
            "NumeroDocumento": 0,
            "Descricao": "",
            "Valor": 0.0
          }
        ],
         
      }
    ],
    "Ciot": {
      "Resultado": 0,
      "Declarado": true,
      "Mensagem": "",
      "Dados": {
        "Ciot": "",
        "Verificador": "",
        "Senha": "",
        "EmContigencia": false,
        "DataDeclaracao": "",
        "AvisoTransportador": ""
      }
    },
    "Pedagio": {
      "Status": 0,
      "Mensagem": "",
      "Valor": 0.0,
      "EstornoSaldoResidualSolicitado": false,
      "ProtocoloValePedagio": "",
      "ProtocoloEnvioValePedagio": "",
      "AvisoTransportador": "",
      "Fornecedor": 2,
    },   
    "IntegracoesPreViagem": {
      "ClienteOrigem": {
        "Sucesso": true,
        "Mensagem": ""
      },
      "ClienteDestino": {
        "Sucesso": true,
        "Mensagem": ""
      },
      "Proprietario": {
        "Sucesso": true,
        "Mensagem": ""
      },
      "Motorista": {
        "Sucesso": true,
        "Mensagem": ""
      },
      "Veiculo": {
        "Sucesso": true,
        "Mensagem": ""
      }
    }
  },
  "Faults": [
    {
        "Type": 0,
        "Code": "",
        "Message": ""
    }    
  ]
}
```

<br>

## <b>Exemplos Específicos - Abastecimento</b>

<br>

### <b>Solicitação de Crédito de Abastecimento</b>

<br>

```json
{
  "Token": "",
  "CnpjAplicacao": "",
  "CNPJEmpresa": "",
  "DocumentoUsuarioAudit": "",
  "NomeUsuarioAudit": "",
  "DadosViagem": {
    "DadosIniciais": {
      "NumeroControle": "CTRL001",
      "DeclaracaoCiot": 0
    },
    "Documentos": {
      "MotoristaDocumento": "12345678901",
      "ProprietarioDocumento": "12345678000195"
    },
    "Veiculo": {
      "Placa": "ABC1234"
    },
    "ViagemEventos": [
      {
        "IdViagemEvento": null,
        "ValorPagamento": 500.00,
        "Status": 2,
        "TipoEvento": 5,
        "Instrucao": "Compra Crédito Abastecimento",
        "HabilitarPagamentoCartao": false,
        "NumeroControle": "DOC123456",
        "DadosAbastecimento": {
          "CodigoCredito": "",
          "CodigoCliente": "CLI001",
          "CodigoProduto": "DIESEL_S10",
          "numerocartao": "1234567890123456",
          "DataValidade": "2024-12-31",
          "DataLiberacao": "2024-01-15",
          "Fornecedor": 0
        }
      }
    ]
  }
}
```

<br>

### <b>Cancelamento de Crédito de Abastecimento</b>

<br>

```json
{
  "IdViagem": 12345,
  "CNPJAplicacao": "",
  "Token": "",
  "CNPJEmpresa": "",
  "ViagemEventos": [
    {
      "IdViagemEvento": 67890,
      "Status": 3,
      "NumeroControle": "CREDITO123",
      "DadosAbastecimento": {
        "CodigoCliente": "CLI001",
        "CodigoProduto": "DIESEL_S10"
      }
    }
  ]
}
```

<br>

**Observações Importantes:**

- **CodigoProduto**: Deve ser enviado pelo TMS conforme especificação do fornecedor
- **Fornecedor**: 0 = TicketLog; 1 = Shell Expers
- **Status**: 2 = Solicitar crédito; 3 = Cancelar crédito
- **HabilitarPagamentoCartao**: Sempre false para fornecedores de abastecimento
- **Placa**: Será enviada automaticamente para o fornecedor baseada no campo "Placa" da viagem