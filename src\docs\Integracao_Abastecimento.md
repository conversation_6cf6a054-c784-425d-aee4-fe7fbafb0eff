<h1><b>Integração de Abastecimento - TicketLog e Shell Expers</b></h1>

<br><h4>Este documento descreve o passo a passo para integração de solicitação e cancelamento de crédito de abastecimento utilizando a estrutura atual da integração de viagem (viagem/integrar) com suporte aos fornecedores TicketLog e Shell Expers.</h4>

<br>

## <b>Visão Geral</b>

A funcionalidade de abastecimento utiliza a estrutura existente da API de viagem, adicionando campos específicos para gerenciar créditos de combustível com os fornecedores:
- **TicketLog** (Fornecedor = 0)
- **Shell Expers** (Fornecedor = 1)

<br>

## <b>Regras de Negócio</b>

### **Solicitação de Crédito**
- Utiliza parcelas **novas** (IdViagemEvento = NULL)
- TipoEvento = 5 (ABASTECIMENTO)
- Status = 2 (Baixado) para realizar o crédito
- HabilitarPagamentoCartao = false (sempre)
- O processo comprará o crédito no fornecedor, **não fará pagamento PIX**

### **Retificação de Viagem**
- Em retificações de viagem já cadastrada, **NÃO** enviar novamente a parcela de ABASTECIMENTO
- Evita duplicação de solicitações

### **Alteração de Parcela**
- Para alterar uma parcela de abastecimento existente:
  1. Enviar cancelamento da parcela atual (baseado no IdViagemEvento)
  2. Enviar nova parcela com os dados atualizados

### **Cancelamento de Crédito**
- Premissa: deve existir uma parcela com solicitação de crédito já enviada
- Status = 3 (Cancelado)
- Informar IdViagemEvento da parcela a ser cancelada

### **Placa do Veículo**
- A placa enviada para TicketLog ou Shell Expers será o campo "Placa" do JSON da Viagem

<br>

## <b>Estrutura de Dados</b>

### **Campos Principais - ViagemEventos**

| Campo | Descrição | Valor para Abastecimento |
|-------|-----------|-------------------------|
| IdViagemEvento | ID do evento da viagem | NULL para parcelas novas |
| ValorPagamento | Valor do pagamento | Valor do crédito para TicketLog/Shell Expers |
| Status | Status do evento | 2 = Solicitar crédito; 3 = Cancelar crédito |
| TipoEvento | Tipo de parcela | 5 = ABASTECIMENTO |
| Instrucao | Informação adicional | "Compra Crédito Abastecimento" |
| HabilitarPagamentoCartao | Habilitar pagamento cartão | false (sempre) |
| NumeroControle | Documento controle | Documento de controle |

### **Estrutura DadosAbastecimento**

| Campo | Descrição | Obrigatório | Tipo |
|-------|-----------|-------------|------|
| CodigoCredito | Código do crédito na TicketLog/Shell Expers | Não | String |
| CodigoCliente | Código do cliente (obtido automaticamente do cadastro de empresa) | Não | String |
| CodigoProduto | Código do produto (deve ser enviado pelo TMS) | **Sim** | String |
| numerocartao | Número do cartão na TicketLog/Shell Expers | Não | String |
| DataValidade | Data de validade (formato yyyy-MM-dd) | Não | Date |
| DataLiberacao | Data de liberação (formato yyyy-MM-dd) | Não | Date |
| Fornecedor | 0 = TicketLog (padrão); 1 = Shell Expers | Não* | Integer |

**Observação:** *Fornecedor é opcional. Se não informado, assume TicketLog (0). Enviar 1 apenas para Shell Expers.

<br>

## <b>Exemplos de Implementação</b>

### **V1 - Solicitação de Crédito TicketLog**

**Endpoint:** `POST /Viagem/Integrar`

```json
{
  "Token": "token-autenticacao",
  "CNPJAplicacao": "12345678000195",
  "CNPJEmpresa": "98765432000198",
  "CPFCNPJClienteDestino": "11111111000111",
  "CPFCNPJClienteOrigem": "22222222000222",
  "CPFCNPJProprietario": "33333333000333",
  "CPFMotorista": "12345678901",
  "Placa": "ABC1234",
  "NumeroControle": "CTRL001",
  "DocumentoUsuarioAudit": "12345678901",
  "NomeUsuarioAudit": "Usuario Sistema",
  "ViagemEventos": [
    {
      "TipoEvento": 5,
      "ValorPagamento": 500.00,
      "Status": 2,
      "HabilitarPagamentoCartao": false,
      "NumeroControle": "DOC123456",
      "Instrucao": "Compra Crédito Abastecimento",
      "DadosAbastecimento": {
        "CodigoCredito": "",
        "CodigoProduto": "DIESEL_S10",
        "numerocartao": "1234567890123456",
        "DataValidade": "2024-12-31",
        "DataLiberacao": "2024-01-15"
      }
    }
  ]
}
```

### **V1 - Solicitação de Crédito Shell Expers**

**Endpoint:** `POST /Viagem/Integrar`

```json
{
  "Token": "token-autenticacao",
  "CNPJAplicacao": "12345678000195",
  "CNPJEmpresa": "98765432000198",
  "CPFCNPJClienteDestino": "11111111000111",
  "CPFCNPJClienteOrigem": "22222222000222",
  "CPFCNPJProprietario": "33333333000333",
  "CPFMotorista": "12345678901",
  "Placa": "ABC1234",
  "NumeroControle": "CTRL001",
  "DocumentoUsuarioAudit": "12345678901",
  "NomeUsuarioAudit": "Usuario Sistema",
  "ViagemEventos": [
    {
      "TipoEvento": 5,
      "ValorPagamento": 500.00,
      "Status": 2,
      "HabilitarPagamentoCartao": false,
      "NumeroControle": "DOC123456",
      "Instrucao": "Compra Crédito Abastecimento",
      "DadosAbastecimento": {
        "CodigoCredito": "",
        "CodigoProduto": "GASOLINA_COMUM",
        "numerocartao": "9876543210987654",
        "DataValidade": "2024-12-31",
        "DataLiberacao": "2024-01-15",
        "Fornecedor": 1
      }
    }
  ]
}
```

### **V2 - Solicitação de Crédito**

**Endpoint:** `POST /ViagemV2/Integrar`

```json
{
  "Token": "token-autenticacao",
  "CnpjAplicacao": "12345678000195",
  "CNPJEmpresa": "98765432000198",
  "DocumentoUsuarioAudit": "12345678901",
  "NomeUsuarioAudit": "Usuario Sistema",
  "DadosViagem": {
    "DadosIniciais": {
      "NumeroControle": "CTRL001",
      "DeclaracaoCiot": 0
    },
    "Documentos": {
      "MotoristaDocumento": "12345678901",
      "ProprietarioDocumento": "33333333000333"
    },
    "Veiculo": {
      "Placa": "ABC1234"
    },
    "ViagemEventos": [
      {
        "IdViagemEvento": null,
        "ValorPagamento": 500.00,
        "Status": 2,
        "TipoEvento": 5,
        "Instrucao": "Compra Crédito Abastecimento",
        "HabilitarPagamentoCartao": false,
        "NumeroControle": "DOC123456",
        "DadosAbastecimento": {
          "CodigoCredito": "",
          "CodigoProduto": "DIESEL_S10",
          "numerocartao": "1234567890123456",
          "DataValidade": "2024-12-31",
          "DataLiberacao": "2024-01-15"
        }
      }
    ]
  }
}
```

### **Cancelamento de Crédito (V1 e V2)**

**Endpoint:** `POST /Viagem/Integrar` ou `POST /ViagemV2/Integrar`

```json
{
  "IdViagem": 12345,
  "CNPJAplicacao": "12345678000195",
  "Token": "token-autenticacao",
  "CNPJEmpresa": "98765432000198",
  "ViagemEventos": [
    {
      "IdViagemEvento": 67890,
      "Status": 3,
      "NumeroControle": "CREDITO123",
      "DadosAbastecimento": {
        "CodigoProduto": "DIESEL_S10"
      }
    }
  ]
}
```

<br>

## <b>Fluxo de Integração</b>

### **1. Solicitação de Crédito**
1. Preparar dados da viagem com TipoEvento = 5
2. Definir Status = 2 (Baixado)
3. Preencher DadosAbastecimento com informações do fornecedor
4. Enviar IdViagemEvento = null para parcelas novas
5. Definir HabilitarPagamentoCartao = false
6. Chamar endpoint de integração de viagem

### **2. Cancelamento de Crédito**
1. Obter IdViagemEvento da parcela a ser cancelada
2. Preparar requisição com Status = 3 (Cancelado)
3. Incluir campos obrigatórios: NumeroControle, CodigoProduto
4. Chamar endpoint de integração de viagem

### **3. Alteração de Crédito**
1. Cancelar parcela existente (Status = 3)
2. Criar nova parcela com dados atualizados (Status = 2)

<br>

## <b>Observações Importantes</b>

- **CodigoProduto**: Deve ser enviado pelo TMS conforme especificação do fornecedor
- **CodigoCliente**: Será obtido automaticamente do cadastro de empresa
- **Placa**: Enviada automaticamente baseada no campo "Placa" da viagem
- **Datas**: Usar formato yyyy-MM-dd
- **Duplicação**: Não reenviar parcelas de abastecimento em retificações
- **Fornecedores**: 0 = TicketLog (padrão); 1 = Shell Expers (opcional)

<br>

## <b>Códigos de Status</b>

| Status | Descrição | Uso |
|--------|-----------|-----|
| 0 | Aberto/Pendente | Parcela criada mas não processada |
| 1 | Bloqueado | Parcela bloqueada |
| 2 | Baixado | **Solicitar crédito de abastecimento** |
| 3 | Cancelado | **Cancelar crédito de abastecimento** |
| 5 | Agendado | Parcela agendada |

<br>

## <b>Fornecedores Suportados</b>

| Código | Fornecedor | Descrição |
|--------|------------|-----------|
| 0 | TicketLog | Fornecedor TicketLog |
| 1 | Shell Expers | Fornecedor Shell Expers |

<br>

## <b>Resposta da API</b>

### **Sucesso - Solicitação de Crédito**
```json
{
  "Sucesso": true,
  "Mensagem": "Viagem integrada com sucesso",
  "Objeto": {
    "IdViagem": 12345,
    "NumeroDocumento": "DOC123456",
    "Eventos": [
      {
        "IdViagemEvento": 67890,
        "NumeroControle": "DOC123456",
        "Token": "evento-token-123",
        "TipoEventoViagem": 5,
        "OperacaoCartao": {
          "Status": 1,
          "Mensagem": "Crédito solicitado com sucesso"
        }
      }
    ]
  }
}
```

### **Sucesso - Cancelamento de Crédito**
```json
{
  "Sucesso": true,
  "Mensagem": "Crédito cancelado com sucesso",
  "Objeto": {
    "IdViagem": 12345,
    "Eventos": [
      {
        "IdViagemEvento": 67890,
        "TipoEventoViagem": 5,
        "OperacaoCartao": {
          "Status": 1,
          "Mensagem": "Cancelamento processado"
        }
      }
    ]
  }
}
```

<br>

## <b>Tratamento de Erros</b>

### **Erros Comuns**

| Erro | Causa | Solução |
|------|-------|---------|
| "CodigoProduto obrigatório" | Campo CodigoProduto não informado | Informar código do produto conforme fornecedor |
| "Fornecedor inválido" | Valor de Fornecedor diferente de 0 ou 1 | Usar 0 para TicketLog ou 1 para Shell Expers |
| "IdViagemEvento não encontrado" | ID informado para cancelamento não existe | Verificar ID correto da parcela |
| "Parcela já cancelada" | Tentativa de cancelar parcela já cancelada | Verificar status atual da parcela |
| "TipoEvento inválido" | TipoEvento diferente de 5 para abastecimento | Usar TipoEvento = 5 |

### **Exemplo de Resposta com Erro**
```json
{
  "Sucesso": false,
  "Mensagem": "Erro na validação dos dados",
  "Faults": [
    {
      "Type": 1,
      "Code": "ABAST001",
      "Message": "CodigoProduto é obrigatório para abastecimento"
    }
  ]
}
```

## <b>Ambientes</b>

### **Homologação**
- **URL**: http://apiho.extratta.com.br:50063/
- **Finalidade**: Testes e validação da integração

### **Produção**
- **URL**: https://api.extratta.com.br:2002/
- **Finalidade**: Operação em ambiente real

<br>






