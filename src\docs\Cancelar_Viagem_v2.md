<h1><b><PERSON><PERSON><PERSON><PERSON> da Viagem (V2)</b></h1>

<br><h4> <PERSON><PERSON><PERSON><PERSON> responsável por cancelar a viagem. </h4>

<br>

## <b>Request</b>

<br>

   * __ENDPOINT: ViagemV2/Cancelar__
   * __VERBO: POST__

<br>

| Índice      | Chave                 | Descrição                                                 | Elem. | Tipo        | Ocor. | Tam. | Versão |
|-------------|-----------------------|-----------------------------------------------------------|-------|-------------|-------|------|--------|
| 1           | Token                 | Token de autenticação                                     | E     | Caractere   | 1-1   | 100  | 1.0    |
| 2           | CNPJAplicacao         | CNPJ para autenticação                                    | E     | Caractere   | 1-1   | 14   | 1.0    |
| 3           | DocumentoUsuarioAudit | Documento do usuário que está realizando a operação       | E     | Caractere   | 1-1   | 14   | 1.0    |
| 4           | NomeUsuarioAudit      | Nome do usuário que está realizando a operação            | E     | Caractere   | 1-1   | 60   | 1.0    |
| 5           | CNPJEmpresa           | CNPJ da empresa responsável pela requisição               | E     | Caractere   | 1-1   | 14   | 1.0    |
| 6           | ViagemId	          | Código da viagem a ser cancelada						  | E     | Inteiro     | 1-1   | 10   | 1.0    |

<br>

## <b>Modelo de Json - Request</b>

<br>

```json
{
  "CNPJAplicacao": "",
  "Token": "",
  "DocumentoUsuarioAudit": "",
  "NomeUsuarioAudit": "",
  "CNPJEmpresa": "",
  "ViagemId": 0
}
```

<br>

## <b>Response</b>

<br>

| Índice      | Chave       | Descrição                                                                                                                                                                                            | Elem. | Tipo      | Ocor.  | Tam.        | Versão |
|-------------|-------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------|-----------|--------|-------------|--------|
| 1           | Sucesso     | __True:__ Caso não tenha acontecido nenhuma exceção ou validação no processo mesmo não trazendo dados no retorno<br>__False:__ Qualquer validação ou exceção que impediu o processo de ser concluído | E     | Booleano  | 1-1    | True/False  | 1.0    |
| 2           | Mensagem    | Mensagem de retorno                                                                                                                                                                                  | E     | Caractere | 1-1    | 100         | 1.0    |
| 3           | Objeto      | Objeto pai que contém os dados																																									   | G     |           | 0-1    |             | 1.0    |
| 3.1         | Sucesso     | __True:__ Caso não tenha acontecido nenhuma exceção ou validação no processo mesmo não trazendo dados no retorno<br>__False:__ Qualquer validação ou exceção que impediu o processo de ser concluído | E     | Booleano  | 1-1    | True/False  | 1.0    |
| 3.2         | Mensagem    | Mensagem de retorno                                                                                                                                                                                  | E     | Caractere | 1-1    | 100         | 1.0    |
| 3.3         | Objeto      | Objeto pai que contém os dados																																									   | G     |           | 1-1    |             | 1.0    |
| 3.3.1       | CodigoFalha | Enum de codigo de falha, podendo ser: <br/> __0:__ Erro; <br/> __1:__ CartaoPedagioPendenteDescarregamento; <br/> __2:__ FalhaEstornoPedagio; <br/> __3:__ SaldoInsuficienteCartaoFrete; <br/> __4:__ FalhaEstornoCartaoFrete; <br/> __5:__ FalhaIntegracaoAntt; <br/> __6:__ FalhaViagemComEventoPagoComCartaFrete; <br/> __7:__ FalhaIntegrarAbastecimento. | E     | Inteiro  | 1-1    | 1        | 1.0    |
| 4           | Faults		| Objeto pai que contém uma lista de erro(s) ou aviso(s) caso aconteça																																   | A     |           | 0-1    |             |        |
| 4.1         | Type		| Tipo de erro ou aviso, podendo ser eles: <br/> __1 -__ Error; <br/> __2 -__ Alert.																												   | E     | Inteiro   | 0-1    | 1           | 1.0    |
| 4.2         | Code		| Codigo do erro																																													   | E     | Caractere | 0-1    | 10          | 1.0    |
| 4.3         | Message		| Mensagem do erro																																													   | E     | Caractere | 0-1    | 200         | 1.0    |

<br>

## <b>Modelo de Json - Response</b>

<br>

```json
{
  "Sucesso": false,
  "Mensagem": "",
  "Objeto": {
	"Sucesso": false,
	"Mensagem": "",
	"Objeto": {
		"CodigoFalha": 3
	}
  }
  "Faults": [
	{
		"Type": 0,
		"Code": "",
		"Message": ""
	}
  ]
}
```