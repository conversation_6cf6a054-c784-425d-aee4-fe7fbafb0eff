# Fluxo Integração Pedágio

Este guia apresenta um fluxo objetivo para cálculo estimado de pedágios e registro de viagem via API da Extratta.

---

## 1. Roteirização da Viagem

**Método:** `POST Viagem/ConsultarCustoPedagioRota`

### Finalidade
Calcular previamente os pedágios da rota e gerar o `IdentificadorHistorico`, necessário para a compra.

### Modos Disponíveis

#### Polyline (linha codificada)
```json
{
  "Polyline": "",
  "TipoVeiculo": 3,
  "QtdEixos": 2
}
```

#### Roteirização Dinâmica
```json
{
  "Localizacoes": [
    {"IbgeCidade": ""},
    {"IbgeCidade": ""}
  ],
  "TipoVeiculo": 3,
  "QtdEixos": 5
}
```

#### Roteirização Fixa
```json
{
  "Localizacoes": [
    {"IdRotaModelo": 0}
  ],
  "TipoVeiculo": 3,
  "QtdEixos": 3
}
```

### Requisição Completa
```json
{
  "CNPJAplicacao": "",
  "CNPJEmpresa": "",
  "Token": "",
  "TipoVeiculo": 1,
  "QtdEixos": 0,
  "ExibirDetalhes": true,
  "Billing": "",
  "TipoRota": 1,
  "Localizacoes": [
    {
      "IbgeCidade": "",
      "Latitude": 0.0,
      "Longitude": 0.0,
      "IdRotaModelo": 0,
      "NomeRota": ""
    }
  ]
}
```

### Retorno da API
```json
{
  "Status": 0,
  "Mensagem": null,
  "CustoTotal": 0.0,
  "CustoTotalTag": 0.0,
  "IdentificadorHistorico": "",
  "KmTotal": 0,
  "TempoPrevisto": "00:00:00",
  "Localizacoes": [
    {
      "cidade": "",
      "estado": "",
      "pais": ""
    }
  ],
  "Pracas": [
    {
      "nome": "",
      "localizacao": {
        "latitude": "",
        "longitude": ""
      },
      "telefone": "",
      "enderecoDescricao": "",
      "concessao": "",
      "codigoAntt": "",
      "viaFacilId": "",
      "fragmentoIndex": 0,
      "precos": [
        {
          "precoEixoAdicional": 0.0,
          "valor": 0.0,
          "valorTag": 0.0
        }
      ]
    }
  ]
}
```

### Como Utilizar o Identificador

Veja abaixo como configurar corretamente sua requisição de viagem dependendo da roteirização escolhida:

#### ✅ Polyline
```json
{
  "Pedagio": {
    "CodPolyline": 123,
    "IdentificadorHistorico": "abc123-hash"
  }
}
```
❌ **Não envie**: `IdRotaModelo`, `NomeRota`

#### ✅ Rota Fixa
```json
{
  "Pedagio": {
    "IdRotaModelo": 0,
    "NomeRota": "Rota Modelo X",
    "IdentificadorHistorico": "abc123-hash"
  }
}
```
❌ **Não envie**: `CodPolyline`

#### ✅ Rota Dinâmica
```json
{
  "Pedagio": {
    "IdentificadorHistorico": "abc123-hash"
  }
}
```
❌ **Não envie**: `CodPolyline`, `IdRotaModelo`, `NomeRota`

---

## 2. Compra de Pedágio (Viagem)

**Método:** `POST ViagemV2/Integrar`

### Requisição Completa
```json
{
  "CNPJAplicacao": "",
  "Token": "",
  "CNPJEmpresa": "",
  "DocumentoUsuarioAudit": "",
  "NomeUsuarioAudit": "",
  "DadosViagem": {
    "CadastrosPreViagem": {
      "ClienteDestino": {},
      "ClienteOrigem": {},
      "Motorista": {},
      "Proprietario": {},
      "Veiculo": {}
    },
    "DadosIniciais": {
      "RealizarIntegracoesPreViagem": true,
      "NumeroControle": ""
    },
    "Documentos": {},
    "Pedagio": {
      "Fornecedor": 0,
      "QtdEixos": 2,
      "TipoVeiculo": 3,
      "CodPolyline": 123,
      "IdRotaModelo": 0,
      "NomeRota": "Rota Sul",
      "IdentificadorHistorico": "abc123-hash"
    },
    "Valores": {
      "Inss": 0
    },
    "Veiculo": {
      "Placa": ""
    },
    "ViagemEventos": []
  }
}
```

### Campos-Chave
| Campo                    | Quando usar                    |
|--------------------------|--------------------------------|
| `IdentificadorHistorico`| Sempre                         |
| `CodPolyline`           | Polyline                       |
| `IdRotaModelo`          | Rota Fixa                      |
| `NomeRota`              | Rota Fixa                      |

---

