# <b>Fluxo de Integração de Pedágio</b>

<br><h4>Guia completo para cálculo estimado de pedágios e registro de viagem via API da Extratta com exemplos práticos, validações e tratamento de erros.</h4>

<br>



## <b>1. Roteirização da Viagem</b>

**Endpoint:** `POST /Viagem/ConsultarCustoPedagioRota`

### **Finalidade**
Calcular previamente os pedágios da rota e gerar o `IdentificadorHistorico`, necessário para a compra do pedágio.

### **Regras Importantes**
- ✅ **Sempre** consulte a rota antes de integrar a viagem
- ✅ O `IdentificadorHistorico` é **obrigatório** para a compra
- ✅ Valide se `CustoTotal > 0` antes de prosseguir


### **Tipos de Roteirização**

#### **🗺️ Polyline (Linha Codificada)**
**Quando usar:** Quando você já possui a rota codificada (ex: Google Maps)

```json
{
  "CNPJAplicacao": "12345678000195",
  "CNPJEmpresa": "98765432000198",
  "Token": "token-autenticacao",
  "Polyline": "u{~vFvyys@fS]",
  "TipoVeiculo": 3,
  "QtdEixos": 2,
  "ExibirDetalhes": true
}
```

#### **🎯 Roteirização Dinâmica**
**Quando usar:** Para calcular rota entre cidades automaticamente

```json
{
  "CNPJAplicacao": "12345678000195",
  "CNPJEmpresa": "98765432000198",
  "Token": "token-autenticacao",
  "Localizacoes": [
    {"IbgeCidade": "4205407"}, // Florianópolis-SC
    {"IbgeCidade": "3550308"}  // São Paulo-SP
  ],
  "TipoVeiculo": 3,
  "QtdEixos": 5,
  "ExibirDetalhes": true
}
```

#### **📋 Roteirização Fixa**
**Quando usar:** Para usar rotas pré-cadastradas no sistema

```json
{
  "CNPJAplicacao": "12345678000195",
  "CNPJEmpresa": "98765432000198",
  "Token": "token-autenticacao",
  "Localizacoes": [
    {"IdRotaModelo": 123}
  ],
  "TipoVeiculo": 3,
  "QtdEixos": 3,
  "ExibirDetalhes": true
}
```

### **Parâmetros Importantes**

| Campo | Descrição | Valores | Obrigatório |
|-------|-----------|---------|-------------|
| TipoVeiculo | Tipo do veículo | 1=Moto, 2=Passeio, 3=Comercial | ✅ Sim |
| QtdEixos | Quantidade de eixos | 2, 3, 4, 5, 6, 7, 8, 9 | ✅ Sim |
| ExibirDetalhes | Mostrar detalhes das praças | true/false | Recomendado |
| Polyline | Linha codificada da rota | String codificada | Conforme tipo |
| IbgeCidade | Código IBGE da cidade | Código de 7 dígitos | Conforme tipo |
| IdRotaModelo | ID da rota pré-cadastrada | Número inteiro | Conforme tipo |

### **Resposta da API**

#### **✅ Sucesso**
```json
{
  "Status": 0,
  "Mensagem": null,
  "CustoTotal": 45.80,
  "CustoTotalTag": 41.22,
  "IdentificadorHistorico": "abc123-def456-ghi789",
  "KmTotal": 702,
  "TempoPrevisto": "08:30:00",
  "Localizacoes": [
    {
      "cidade": "Florianópolis",
      "estado": "SC",
      "pais": "Brasil"
    },
    {
      "cidade": "São Paulo",
      "estado": "SP",
      "pais": "Brasil"
    }
  ],
  "Pracas": [
    {
      "nome": "Praça de Pedágio Palhoça",
      "localizacao": {
        "latitude": "-27.6394",
        "longitude": "-48.6277"
      },
      "telefone": "(48) 3242-0000",
      "enderecoDescricao": "BR-101, Km 210",
      "concessao": "Autopista Litoral Sul",
      "codigoAntt": "001234",
      "viaFacilId": "VF001",
      "fragmentoIndex": 0,
      "precos": [
        {
          "precoEixoAdicional": 5.40,
          "valor": 8.90,
          "valorTag": 8.01
        }
      ]
    }
  ]
}
```

#### **❌ Erro**
```json
{
  "Status": 1,
  "Mensagem": "Rota não encontrada entre as localizações informadas",
  "CustoTotal": 0.0,
  "CustoTotalTag": 0.0,
  "IdentificadorHistorico": null,
  "KmTotal": 0,
  "TempoPrevisto": "00:00:00",
  "Localizacoes": [],
  "Pracas": []
}
```

### **Validações Importantes**

✅ **Antes de prosseguir, verifique:**
- `Status == 0` (sucesso)
- `CustoTotal > 0` (há pedágios na rota)
- `IdentificadorHistorico` não está vazio
- `Pracas.length > 0` (há praças na rota)

### Como Utilizar o Identificador

Veja abaixo como configurar corretamente sua requisição de viagem dependendo da roteirização escolhida:

#### ✅ Polyline
```json
{
  "Pedagio": {
    "CodPolyline": 123,
    "IdentificadorHistorico": "abc123-hash"
  }
}
```
❌ **Não envie**: `IdRotaModelo`, `NomeRota`

#### ✅ Rota Fixa
```json
{
  "Pedagio": {
    "IdRotaModelo": 0,
    "NomeRota": "Rota Modelo X",
    "IdentificadorHistorico": "abc123-hash"
  }
}
```
❌ **Não envie**: `CodPolyline`

#### ✅ Rota Dinâmica
```json
{
  "Pedagio": {
    "IdentificadorHistorico": "abc123-hash"
  }
}
```
❌ **Não envie**: `CodPolyline`, `IdRotaModelo`, `NomeRota`

---

## 2. Compra de Pedágio (Viagem)

**Método:** `POST ViagemV2/Integrar`

### Requisição Completa
```json
{
  "CNPJAplicacao": "",
  "Token": "",
  "CNPJEmpresa": "",
  "DocumentoUsuarioAudit": "",
  "NomeUsuarioAudit": "",
  "DadosViagem": {
    "CadastrosPreViagem": {
      "ClienteDestino": {},
      "ClienteOrigem": {},
      "Motorista": {},
      "Proprietario": {},
      "Veiculo": {}
    },
    "DadosIniciais": {
      "RealizarIntegracoesPreViagem": true,
      "NumeroControle": ""
    },
    "Documentos": {},
    "Pedagio": {
      "Fornecedor": 0,
      "QtdEixos": 2,
      "TipoVeiculo": 3,
      "CodPolyline": 123,
      "IdRotaModelo": 0,
      "NomeRota": "Rota Sul",
      "IdentificadorHistorico": "abc123-hash"
    },
    "Valores": {
      "Inss": 0
    },
    "Veiculo": {
      "Placa": ""
    },
    "ViagemEventos": []
  }
}
```


