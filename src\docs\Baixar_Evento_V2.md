<h1><b>Bai<PERSON><PERSON> evento da viagem (V2)</b></h1>

<br><h4> Este método é responsável por realizar pagamento de frete associados a uma viagem e/ou parcela específica. </h4>

<br>

## <b>Request</b>

<br>

   * __ENDPOINT: ViagemV2/BaixarEvento__  
   * __VERBO: PUT__ 

<br>

| Índice      | Chave				  | Descrição                                                 | Elem. | Tipo        | Ocor. | Tam. | Versão |
|-------------|-----------------------|-----------------------------------------------------------|-------|-------------|-------|------|--------|
| 1           | Token				  | Token de autenticação                                     | E     | Caractere   | 1-1   | 100  | 1.0    |
| 2           | CNPJAplicacao		  | CNPJ para autenticação                                    | E     | Caractere   | 1-1   | 14   | 1.0    |
| 3           | CNPJEmpresa			  | CNPJ da empresa responsável pela requisição               | E     | Caractere   | 1-1   | 14   | 1.0    |
| 4           | DocumentoUsuarioAudit | Documento do usuário que está realizando a operação       | E     | Caractere   | 1-1   | 14   | 1.0    |
| 5           | NomeUsuarioAudit	  | Nome do usuário que está realizando a operação            | E     | Caractere   | 1-1   | 60   | 1.0    |
| 6           | ViagemId			  | Código da viagem que contém o evento					  | E     | Inteiro     | 1-1   | 10   | 1.0    |
| 7           | ViagemEventoId		  | Código da evento										  | E     | Inteiro     | 1-1   | 10   | 1.0    |

<br>




## <b>Modelo de Json - Request</b>

<br>

```json
{
  
  "Token": "",
  "CNPJAplicacao": "",
  "CNPJEmpresa ": "",
  "DocumentoUsuarioAudit": "",
  "NomeUsuarioAudit": "",
  "ViagemId": 0,
  "ViagemEventoId": 0
}
```

<br>

## <b>Response</b>

<br>

| Índice      | Chave       | Descrição                                                                                                                                                                                            | Elem. | Tipo      | Ocor.  | Tam.        | Versão |
|-------------|-------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------|-----------|--------|-------------|--------|
| 1           | Sucesso     | __True:__ Caso não tenha acontecido nenhuma exceção ou validação no processo mesmo não trazendo dados no retorno<br>__False:__ Qualquer validação ou exceção que impediu o processo de ser concluído | E     | Booleano  | 1-1    | True/False  | 1.0    |
| 2           | Mensagem    | Mensagem de retorno                                                                                                                                                                                  | E     | Caractere | 1-1    | 100         | 1.0    |
| 3           | Faults		| Objeto pai que contém uma lista de erro(s) ou aviso(s) caso aconteça																																   | A     |           | 0-1    |             |        |
| 3.1         | Type		| Tipo de erro ou aviso, podendo ser eles: <br/> __1 -__ Error; <br/> __2 -__ Alert.																												   | E     | Inteiro   | 0-1    | 1           | 1.0    |
| 3.2         | Code		| Codigo do erro																																													   | E     | Caractere | 0-1    | 10          | 1.0    |
| 3.3         | Message		| Mensagem do erro																																													   | E     | Caractere | 0-1    | 200         | 1.0    |

<br>

## <b>Modelo de Json - Response</b>

<br>

```json
{
  "Sucesso": false,
  "Mensagem": "",
  "Faults": [
	{
		"Type": 0,
		"Code": "",
		"Message": ""
	}
  ]
}
```