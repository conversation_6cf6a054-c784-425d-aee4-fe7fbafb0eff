<h1><b>Liquidação/Pagamento do Frete </b></h1>

<br><h4> Este método é responsável por realizar a alteração dos pagamentos de frete associados a uma viagem específica. Ele valida as condições de pagamento e atualiza os registros de acordo com as modificações informadas no processo.</h4>

<h4><b>Pontos Importantes: </b></h4>
+ É obrigatório informar o ID do evento e o número de controle para garantir a consistência e segurança durante as alterações.
+ Caso seja necessário incluir um novo evento na viagem, o parâmetro IDViagemEvento deve ser enviado como null, indicando que o sistema deve gerar um novo identificador automaticamente.

<br>

## <b>Request</b>

<br>

   * __ENDPOINT: Viagem/Integrar__
   * __VERBO: POST__

<br>

| Índice      | Chave                     | Descrição                                                                                                                                                                                                                                                                                                                      | Elem. | Tipo      | Ocor.    | Tam.       | Versão |
|-------------|---------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------|-----------|----------|------------|--------|
| 1           | IdViagem                  | Ao integrar uma viagem é gerado um ID, o qual deverá ser armazenado na estrutura do ERP/TMS e informado nesse campo durante a atualização da viagem                                                                                                                                                                            | E     | Inteiro   | 1-1      | 10         | 1.0    |
| 2           | Token                     | Gerado a partir do CNPJ de autenticação para validar permissões de acesso                                                                                                                                                                                                                                                      | E     | Caractere | 1-1      | 100        | 1.0    |
| 3           | CNPJAplicacao             | CNPJ para autenticação                                                                                                                                                                                                                                                                                                         | E     | Caractere | 1-1      | 14         | 1.0    |
| 4           | DocumentoUsuarioAudit     | Documento do usuário que está realizando a operação                                                                                                                                                                                                                                                                            | E     | Caractere | 1-1      | 14         | 1.0    |
| 5           | NomeUsuarioAudit          | Nome do usuário que está realizando a operação                                                                                                                                                                                                                                                                                 | E     | Caractere | 1-1      | 60         | 1.0    |
| 6           | CNPJEmpresa               | CNPJ da empresa que está cadastrando a viagem                                                                                                                                                                                                                                                                                  | E     | Caractere | 1-1      | 14         | 1.0    |
| 5           | DadosPagamento            | Dados bancários                                                                                                                                                                                                                                                                                                                | G     |           | 1-0      | -          | 1.0    |
| 5.1         | FormaPagamento            | Forma de Pagamento da Viagem. Caso não informado, será utilizado o valor 5 - Outros. Os atuais valores são: <br/>__1-__ Cartao;<br/>__2-__ ContaCorrente;<br/>__3-__ ContaPoupanca;<br/>__4-__ ContaPagament;,<br/>__5-__ Outros.;                                                                                             | E     | Inteiro   | 1-0      | 1          | 1.0    |
| 5.2         | CodigoBacen               | Código BACEN do banco para pagamento em conta (Forma de pagamento 2, 3 e 4)                                                                                                                                                                                                                                                    | E     | Caractere | 1-0      | 5          | 1.0    |
| 5.3         | Agencia                   | Agência do banco para pagamento em conta (Forma de pagamento 2, 3 e 4)                                                                                                                                                                                                                                                         | E     | Caractere | 1-0      | 10         | 1.0    |
| 5.4         | Conta                     | Conta do banco para pagamento em conta (Forma de pagamento 2, 3 e 4)                                                                                                                                                                                                                                                           | E     | Caractere | 1-0      | 10         | 1.0    |
| 6           | ViagemEventos             | Parcelas da Viagem                                                                                                                                                                                                                                                                                                             | G     |           | 1-0      | -          | 1.0    |
| 6.1         | IdViagemEvento            | Ao integrar é gerado um ID (PK), o qual deverá ser armazenado na estrutura do ERP/TMS para ser informado quando necessário a edição do registro                                                                                                                                                                                | E     | Inteiro   | 1-0      | 10         | 1.0    |
| 6.2         | TipoEvento                | Tipo do evento a ser pago na viagem. <br/>__0:__ Adiantamento;<br/>__1:__ Saldo;<br/>__2:__ Estadia;<br/>__3:__ RPA;<br/>__4:__ Tarifa ANTT;<br/>__5:__ Abastecimento.                                                                                                                                                         | E     | Inteiro   | 1-1      | 10         | 1.0    |
| 6.3         | ValorPagamento            | Valor do pagamento                                                                                                                                                                                                                                                                                                             | E     | Decimal   | 1-1      | 10,2       | 1.0    |
| 6.4         | Status                    | Status do evento de pagamento:<br/>__0:__ Aberto/Pendente;<br/>__1:__ Bloqueado;<br/>__2:__ Baixado (Efetivado/Liquidado);<br/>__3:__ Cancelado;<br/>__5:__ Agendado.<br/>**Para abastecimento:** Status 2 = Solicitar crédito; Status 3 = Cancelar crédito.                                                                                                                                                          | E     | Inteiro   | 1-1      | 10         | 1.0    |
| 6.5         | HabilitarPagamentoCartao  | Ao definir o parâmetro de pagamento como __true__, a transação será processada via Extratta (Cartão).<br/> Caso o proprietário não tenha um cartão vinculado, o sistema criará automaticamente uma conta virtual, na qual o valor será disponibilizado.<br/> Se o parâmetro pagamento for definido como __false__, dois cenários são possíveis:<br/>__1-__ O pagamento poderá ser registrado como depósito (transação não irá ocorrer pela Extratta);<br/>__2-__ O pagamento poderá ser realizado via PIX através da Extratta, desde que o cliente tenha contratado o serviço de pagamento via PIX e o proprietário tenha uma chave PIX válida cadastrada.        | E     | Booleano  | 1-1      | 1          | 1.0    |
| 6.6         | NumeroControle            | Número único para identificação da parcela. Não é possível integrar 2 eventos com o mesmo número controle                                                                                                                                                                                                                      | E     | Caractere | 1-0      | 300        | 1.0    |

<br>

## <b>Modelo de Json - Request</b>

<br>

```json
{
  "IdViagem": 0,
  "Token": "",
  "CNPJAplicacao": "",
  "DocumentoUsuarioAudit": "",
  "NomeUsuarioAudit": "",
  "CNPJEmpresa": "",
  "DadosPagamento": [
    {
      "FormaPagamento": 1,
      "CodigoBacen": "",
      "Agencia": "",
      "Conta": ""
    }
  ],
  "ViagemEventos": [
    {
      "IdViagemEvento": "",
      "TipoEvento": 0,
      "ValorPagamento": 0.0,
      "Status": 2,
      "HabilitarPagamentoCartao": "True",
      "NumeroControle": ""
    }
  ]
}
```

<br>

## <b>Response</b>

<br>

| Índice      | Chave                           | Descrição                                                                                                                                                                                                                        | Elem. | Tipo        | Ocor.   | Tam. | Versão |
|-------------|---------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------|-------------|---------|------|--------|
| 1           | Sucesso                         | __True:__ Caso não tenha acontecido nenhuma exceção ou validação no processo mesmo não trazendo dados no retorno; <br/>__False:__ Qualquer validação ou exceção não tratada pelo sistema que impediu o processo de ser concluído | E     | Booleano    | 1-1     |      | 1.0    |
| 2           | Mensagem                        | Mensagem de retorno                                                                                                                                                                                                              | E     | Caractere   | 1-1     | 100  | 1.0    |
| 3           | Object                          | Objeto pai que contém uma lista de eventos da viagem (parcelas)                                                                                                                                                                  | G     |             | 0-1     |      | 1.0    |
| 3.1         | IdViagem                        | Código da viagem – deve ser armazenado este Id para retificar/alterar a respectiva viagem                                                                                                                                        | E     | Inteiro     | 1-1     | 10   | 1.0    |
| 3.2         | NumeroDocumento                 | Numero do documento                                                                                                                                                                                                              | E     | Caractere   | 1-1     | 14   | 1.0    |
| 3.3         | IdsViagemEstabelecimento        | Lista de ids dos estabelecimentos da viagem                                                                                                                                                                                      | A     | Inteiro     | 0-1     | 10   | 1.0    |
| 3.4         | IRRPF                           | IRRPF da viagem                                                                                                                                                                                                                  | E     | Inteiro     | 0-1     | 10   | 1.0    |
| 3.5         | INSS                            | INSS da viagem                                                                                                                                                                                                                   | E     | Inteiro     | 0-1     | 10   | 1.0    |
| 3.6         | SESTSENAT                       | SEST/SENAT da viagem                                                                                                                                                                                                             | E     | Inteiro     | 0-1     | 10   | 1.0    |
| 3.7         | Eventos                         | Objeto pai que contém uma lista de eventos da viagem (parcelas)                                                                                                                                                                  | A     |             | 0-1     |      | 1.0    |
| 3.7.1       | OperacaoCartao                  | Informações referente ao sucesso quanto a carga de valor no cartão.                                                                                                                                                              | G     |             | 0-1     |      | 1.0    |
| 3.7.1.1     | Status                          | Enum referente Situação da transação realizada com o cartão do motorista, podendo ser: <br/>__0:__ Pendente;<br/>__1:__ Sucesso;<br/>__2:__ Erro;<br/>__3:__ NaoHabilitado.                                                      | E     | Inteiro     | 0-1     | 1    | 1.0    |
| 3.7.1.2     | Mensagem                        | Mensagem com indicador de falha do processo                                                                                                                                                                                      | E     | Caractere   | 0-1     | 200  | 1.0    |
| 3.7.2       | IdViagemEvento                  | Código do evento da viagem – deve ser armazenado este Id para retificar/alterar o respectivo evento                                                                                                                              | E     | Inteiro     | 0-1     | 10   | 1.0    |
| 3.7.3       | NumeroControle                  | Número de controle do evento para ERP                                                                                                                                                                                            | E     | Caractere   | 0-1     | 20   | 1.0    |
| 3.7.4       | Token                           | Token do evento da viagem                                                                                                                                                                                                        | E     | Caractere   | 1-1     | 100  | 1.0    |
| 3.7.5       | Instrucao                       | Instrução do evento da viagem/Observações                                                                                                                                                                                        | E     | Caractere   | 0-1     | 100  | 1.0    |
| 3.7.6       | IdsViagemOutrosDescontos        | Lista de códigos de outros descontos                                                                                                                                                                                             | A     | Inteiro     | 0-1     | 10   | 1.0    |
| 3.7.7       | TipoEventoViagem                | Tipo de evento de viagens: <br/>__0:__ Adiantamento;<br/>__1:__ Saldo;<br/>__2:__ Estadia;<br/>__3:__ RPA;<br/>__4:__ Tarifa ANTT;<br/>__5:__ Abastecimento                                                                      | E     | Inteiro     | 0-1     | 1    | 1.0    |
| 3.7.8       | ValorBruto                      | Valor bruto                                                                                                                                                                                                                      | E     | Decimal     | 0-1     | 10,2 | 1.0    |
| 3.7.9       | IdsViagemDocumento              | Lista de códigos dos documentos da viagem                                                                                                                                                                                        | A     | Inteiro     | 0-1     | 10   | 1.0    |
| 3.7.10      | ViagemOutrosDescontos           | Objeto pai que contém uma lista de outros descontos relacionados a um evento da viagem                                                                                                                                           | A     | Inteiro     | 0-1     | 1    | 1.0    |
| 3.7.10      | ViagemOutrosAcrescimos          | Objeto pai que contém uma lista de outros acréscimos relacionados a um evento da viagem                                                                                                                                          | A     | Inteiro     | 0-1     | 1    | 1.0    |
| 3.8         | CIOT                            | Grupo de informações relacionadas ao CIOT da viagem integrada                                                                                                                                                                    | G     |             | 0-1     |      | 1.0    |
| 3.8.1       | Declarado                       | Indica que a viagem foi gerada com o respectivo CIOT                                                                                                                                                                             | E     | Booleano    | 1-1     | True/False | 1.0    |
| 3.8.2       | Resultado                       | Enum que indica o status da declaração de CIOT, podendo ser:  <br/>__0:__ Sucesso;<br/>__1:__ Erro;<br/>__2:__ NaoObrigatorio;<br/>__3:__ NaoHabilitado.                                                                         | E     | Inteiro     | 1-1     | 1    | 1.0    |
| 3.8.3       | Mensagem                        | Mensagem informativa sobre a declaração do CIOT                                                                                                                                                                                  | E     | Caractere   | 0-1     | 200  | 1.0    |
| 3.8.4       | Dados                           | Grupo de informações com os dados integrados na ANTT referente ao CIOT                                                                                                                                                           | G     |             | 0-1     |      | 1.0    |
| 3.8.4.1     | Ciot                            | Número do CIOT gerado pelo meio homologado                                                                                                                                                                                       | E     | Caractere   | 1-1     | 12   | 1.0    |
| 3.8.4.2     | Verificador                     | Número verificador do CIOT gerado pela ANTT                                                                                                                                                                                      | E     | Caractere   | 1-1     | 4    | 1.0    |
| 3.8.4.3     | Senha                           | Senha de acesso ao CIOT gerada pelo meio homologado                                                                                                                                                                              | E     | Caractere   | 1-1     | 20   | 1.0    |
| 3.8.4.4     | EmContigencia                   | Informativo verdadeiro/falso indicando se a viagem foi declarada em contingência na ANTT                                                                                                                                         | E     | Booleano    | 1-1     | True/False | 1.0    |
| 3.8.4.5     | DataDeclaracao                  | Data e hora da integração com ANTT                                                                                                                                                                                               | E     | Caractere   | 1-1      | yyyy-MM-dd HH:mm:ss | 1.0    |
| 3.8.4.6     | AvisoTransportador              | Mensagem de aviso cadastrada pela ANTT para o contratado                                                                                                                                                                         | E     | Caractere   | 0-1     | 300  | 1.0    |
| 3.8.5       | DadosANTT                       | Dados para ANTT                                                                                                                                                                                                                  | G     |             | 0-1     |      | 1.0    |
| 3.8.5.1     | AltoDesempenho                  | Indicação se veículo é de alto desempenho                                                                                                                                                                                        | E     | Booleano    | 0-1      | True/False | 1.0    |
| 3.8.5.2     | DestinacaoComercial             | Indicação se destinação é comercial                                                                                                                                                                                              | E     | Booleano    | 0-1      | True/False | 1.0    |
| 3.8.5.3     | FreteRetorno                    | Indicação se há frete de retorno                                                                                                                                                                                                 | E     | Booleano    | 0-1      | True/False | 1.0    |
| 3.8.5.4     | CEPRetorno                      | CEP do frete de retorno. Necessário informar caso o campo FreteRetorno esteja true.                                                                                                                                              | E     | Caractere   | 0-1      | 8          | 1.0    |
| 3.8.5.5     | DistanciaRetorno                | Distância do frete de retorno em KM. Necessário informar caso o campo FreteRetorno esteja true.                                                                                                                                  | E     | Inteiro     | 0-1      | 10         | 1.0    |
| 3.9         | Pedagio                         | Grupo de informação relacionadas a compra do pedágio caso habilitada                                                                                                                                                             | G     |             | 0-1     | 1    | 1.0    |
| 3.9.1       | Status                          | Enum indicador de sucesso na comunicação com fornecedor de pedágio, podendo ser eles: <br/> __0 -__ CompraSolicitada; <br/> __1 -__ Erro;<br/> __2 -__ NaoRealizado;<br/> __3 -__ CancelamentoSolicitado;<br/> __4 -__ CompraConfirmada;<br/> __5 -__ CancelamentoConfirmado. | E     | Inteiro     | 1-1     | 1    | 1.0    |
| 3.9.2       | Mensagem                        | Mensagem com indicador da mensagem de falha do processo                                                                                                                                                                          | E     | Caractere   | 0-1     | 200  | 1.0    |
| 3.9.3       | Valor                           | Valor solicitado para carga de pedágio                                                                                                                                                                                           | E     | Decimal     | 1-1     | 18,2 | 1.0    |
| 3.9.4       | ProtocoloRequisicao             | Número de protocolo do registro de requisição de pedágio para ANTT                                                                                                                                                               | E     | Caractere   | 0-1     | 30   | 1.0    |
| 3.9.5       | ProtocoloProcessamento          | Número de protocolo do registro de processamento do pedágio na ANTT                                                                                                                                                              | E     | Caractere   | 0-1     | 30   | 1.0    |
| 3.9.6       | EstornoSaldoResidualSolicitado  | Existe estorno, sim ou nao                                                                                                                                                                                                       | E     | Booleano    | 0-1     | True/False | 1.0    |
| 3.9.7       | ProtocoloValePedagio            | Número de protocolo do registro de pedágio na ANTT                                                                                                                                                                               | E     | Caractere   | 0-1     | 30   | 1.0    |
| 3.9.8       | ProtocoloEnvioValePedagio       | Número de protocolo do registro de envio do pedágio para ANTT                                                                                                                                                                    | E     | Caractere   | 0-1     | 30   | 1.0    |
| 3.9.9       | AvisoTransportador              | Mensagem de aviso                                                                                                                                                                                                                | E     | Caractere   | 0-1     | 30   | 1.0    |
| 3.9.10      | Fornecedor                      | Parceiro fornecedor de pedágio para efetuar compra: <br/>__0:__ Desabilitado (padrão);<br/>__1:__ Moedeiro: Carga no cartão moedeiro Extratta (__Descontinuado pela ANTT__);<br/>__2:__ Via Fácil (Sem Parar); <br/>__3:__ Move Mais;<br/>__4:__ Veloe <br/>__5:__ Tag Extratta  <br/>__6:__ ConectCar; <br/>__7:__ Taggy Edenred. | E     | Inteiro   | 0-1     | 1    | 1.0    |
| 3.9.11      | CNPJFornecedor                  | CNPJ do Fornecedor de Vale Pedagio                                                                                                                                                                                               | E     | Caractere   | 0-1     | 20    | 1.0    |
| 3.9.12      | CompraCredenciaisExtratta       | Se a compra do Vale Pedagio foi feita com as credenciais da extratta                                                                                                                                                             | E     | Booleano    | 0-1     | True/False  1.0    |
| 4           | Faults                          | Objeto pai que contém uma lista de erro(s) ou aviso(s) caso aconteça                                                                                                                                                             | A     |           |        |       |        |
| 4.1         | Type                            | Tipo de erro ou aviso, podendo ser eles: <br/> __1 -__ Error; <br/> __2 -__ Alert.                                                                                                                                               | E     | Inteiro   | 0-1    | 1     | 1.0    |
| 4.2         | Code                            | Codigo do erro                                                                                                                                                                                                                   | E     | Caractere | 0-1    | 10    | 1.0    |
| 4.3         | Message                         | Mensagem do erro                                                                                                                                                                                                                 | E     | Caractere | 0-1    | 100   | 1.0    |

<br>

## <b>Modelo de Json - Response</b>

<br>

```json
{
  "Sucesso": true,
  "Mensagem": null,
  "Objeto": {
    "IdViagem": 0,
    "NumeroDocumento": "",
    "IdsViagemEstabelecimento": [],
    "Eventos": [
      {
        "OperacaoCartao": {
          "Status": 0,
          "Mensagem": ""
        },
        "IdViagemEvento": ,
        "NumeroControle": "",
        "Token": "",
        "IdsViagemDocumento": [],
        "IdsViagemVlAdicional": null,
        "IdsViagemOutrosDescontos": [],
        "ViagemOutrosDescontos": [],
        "IdsViagemOutrosAcrescimos": [],
        "ViagemOutrosAcrescimos": [],
        "TipoEventoViagem": 1,
        "ValorBruto": 0.0
      }
    ],
    "CIOT": {
      "Resultado": 0,
      "Declarado": true,
      "Mensagem": null,
      "Dados": {
        "Ciot": "",
        "Verificador": "",
        "Senha": "",
        "EmContigencia": false,
        "DataDeclaracaoDateTime": "",
        "DataDeclaracao": "",
        "AvisoTransportador": null
      },
      "DadosAntt": {
        "AltoDesempenho ": false,
        "DestinacaoComercial": false,
        "FreteRetorno": false,
        "CEPRetorno": "",
        "DistanciaRetorno": 0

      }
    },
    "Pedagio": {
      "Status": 0,
      "Mensagem": "",
      "Valor": 0.0,
      "ProtocoloRequisicao": "",
      "ProtocoloProcessamento": "",
      "EstornoSaldoResidualSolicitado": false,
      "ProtocoloValePedagio": "",
      "ProtocoloEnvioValePedagio": "",
      "AvisoTransportador": null,
      "Fornecedor": 0,
      "CnpjFornecedor": "",
      "CompraCredenciaisExtratta": false
    },
    "IRRPF": 0.0,
    "INSS": 0.0,
    "SESTSENAT": 0.0,
    "Avisos": null
  },
   "Faults": [
    {
        "Type": 0,
        "Code": "",
        "Message": ""
    }
  ]
}
```