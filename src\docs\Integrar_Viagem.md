<h1><b> Integração de Viagem (V1) </b></h1>

<br> <h4> Realiza a integração de uma viagem para geração de CIOT, Pagamento Eletrônico de Frete e/ou Vale Pedágio.
Para geração de Vale Pedágio Avulso deve ser integrada uma viagem sem declarar o CIOT;
<br> Antes de requisitar o método de integração de viagem devem ser requisitados/invocados os métodos de cadastros; </h4>

<br>Através deste método é possível:

+ Geração de CIOT (somente declarar o CIOT);
+ Geração de CIOT, Pagamento de Frete (Cartão ou Conta Virtual);
+ Geração de CIOT, Pagamento de Frete (Depósito) e Vale Pedágio TAG;
+ Geração de CIOT, Pagamento de Frete (Cartão ou Conta Virtual) e Vale Pedágio TAG
+ Geração de Viagem para Vale Pedágio Avulso (sem emissão de CIOT e Pagamento de Frete no Cartão ou Conta Virtual);

<br> Regras:

+ __CIOT PADRÃO__: Cancelamento de uma Operação de Transporte: a Operação de Transporte, desde que
não tenha sido consultada pela fiscalização da ANTT, poderá ser cancelada em até 24h após a data de emissão.

+ __CIOT PERIÓDICO (TAC AGREGADO)__: Será permitido o cancelamento do cadastro da Operação de Transporte do tipo
TAC-agregado, desde que ele não tenha sido consultado pela fiscalização da ANTT, em até 5 dias
da abertura.

<br>

## <b>Request</b>

<br>

   * __ENDPOINT: Viagem/Integrar__
   * __VERBO: POST__

<br>

| Índice      | Chave                     | Descrição                                                                                                                                                                                                                                                                                                                      | Elem. | Tipo      | Ocor.    | Tam.       | Versão |
|-------------|---------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------|-----------|----------|------------|--------|
| 1           | CNPJAplicacao             | CNPJ para autenticação                                                                                                                                                                                                                                                                                                         | E     | Caractere | 1-1      | 14         | 1.0    |
| 2           | CNPJEmpresa               | CNPJ da empresa que está cadastrando a viagem                                                                                                                                                                                                                                                                                  | E     | Caractere | 1-1      | 14         | 1.0    |
| 3           | CPFCNPJClienteDestino     | CPF ou CNPJ do cliente de destino                                                                                                                                                                                                                                                                                              | E     | Caractere | 1-1      | 14         | 1.0    |
| 4           | CPFCNPJClienteOrigem      | CPF ou CNPJ do cliente de origem                                                                                                                                                                                                                                                                                               | E     | Caractere | 1-1      | 14         | 1.0    |
| 5           | CPFCNPJClienteTomador     | CPF ou CNPJ do cliente tomador                                                                                                                                                                                                                                                                                                 | E     | Caractere | 1-0      | 14         | 1.0    |
| 6           | CPFCNPJProprietario       | Documento do proprietário/Contratado                                                                                                                                                                                                                                                                                           | E     | Caractere | 1-0      | 100        | 1.0    |
| 7           | NomeProprietario          | Nome do proprietário/Contratado                                                                                                                                                                                                                                                                                                | E     | Caractere | 1-0      | 100        | 1.0    |
| 8           | RNTRC                     | RNTRC                                                                                                                                                                                                                                                                                                                          | E     | Caractere | 1-0      | 10         | 1.0    |
| 9           | CNPJFilial                | Documento da filial                                                                                                                                                                                                                                                                                                            | E     | Caractere | 1-0      | 100        | 1.0    |
| 10          | RazaoSocialFilial         | Razão social da filial                                                                                                                                                                                                                                                                                                         | E     | Caractere | 1-0      | 100        | 1.0    |
| 11          | CPFMotorista              | CPF do motorista que realizará a entrega                                                                                                                                                                                                                                                                                       | E     | Caractere | 1-1      | 11         | 1.0    |
| 12          | Placa                     | Placa do CAVALO                                                                                                                                                                                                                                                                                                                | E     | Caractere | 1-1      | 7          | 1.0    |
| 13          | DataColeta                | Data de Inicio da Viagem. A Data nao pode ser retroativa                                                                                                                                                                                                                                                                       | E     | Data      | 1-0      | dd/MM/yy   | 1.0    |
| 14          | DataEmissao               | Data de emissão (início de viagem)                                                                                                                                                                                                                                                                                             | E     | Data      | 1-0      | dd/MM/yy   | 1.0    |
| 15          | DataPrevisaoEntrega       | Data Fim de Viagem                                                                                                                                                                                                                                                                                                             | E     | Data      | 1-0      | dd/MM/yy   | 1.0    |
| 16          | DocumentoCliente          | Número do documento na nota. Numeração para o cliente.                                                                                                                                                                                                                                                                         | E     | Caractere | 1-0      | 100        | 1.0    |
| 17          | NumeroDocumento           | Número do documento fiscal                                                                                                                                                                                                                                                                                                     | E     | Caractere | 1-0      | 100        | 1.0    |
| 18          | NumeroControle            | Número de controle para não duplicar viagens                                                                                                                                                                                                                                                                                   | E     | Caractere | 1-1      | 300        | 1.0    |
| 19          | StatusViagem              | Enum de status de viagem, pondendo ser eles:<br/>__1:__ Aberto (Padrão);<br/>__2:__ Programada;<br/>__3:__ Cancelada;<br/>__4:__ Bloqueada;<br/>__5:__ Baixada.                                                                                                                                                                | E     | Inteiro   | 1-0      | 1          | 1.0    |
| 20          | Carretas                  | Lista de placas referenciando as carretas viagem (Array informando as placas de carreta)                                                                                                                                                                                                                                       | E     | Caractere | 1-0      | 50         | 1.0    |
| 21          | PesoSaida                 | Peso do frete detalhado no documento de transporte                                                                                                                                                                                                                                                                             | E     | Decimal   | 1-0      | 10,3       | 1.0    |
| 22          | ValorMercadoria           | Valor total da mercadoria                                                                                                                                                                                                                                                                                                      | E     | Decimal   | 1-0      | 10,2       | 1.0    |
| 23          | ValorFrete                | Valor Total do Frete                                                                                                                                                                                                                                                                                                           | E     | Decimal   | 1-0      | 10,2       | 1.0    |
| 24          | IRRPF                     | Valor do imposto                                                                                                                                                                                                                                                                                                               | E     | Decimal   | 1-0      | 10,2       | 1.0    |
| 25          | INSS                      | Valor do imposto                                                                                                                                                                                                                                                                                                               | E     | Decimal   | 1-0      | 10,2       | 1.0    |
| 26          | SESTSENAT                 | Valor do imposto                                                                                                                                                                                                                                                                                                               | E     | Decimal   | 1-0      | 10,2       | 1.0    |
| 27          | Produto                   | Descrição do produto da viagem                                                                                                                                                                                                                                                                                                 | E     | Caractere | 1-0      | 100        | 1.0    |
| 28          | Unidade                   | Enum de unidade de medida do produto, podendo ser eles: <br/>__0:__ Peso; <br/>__1:__ Saca.                                                                                                                                                                                                                                    | E     | Inteiro   | 1-0      | 1          | 1.0    |
| 29          | Quantidade                | Quantidade referente a carga                                                                                                                                                                                                                                                                                                   | E     | Decimal   | 1-0      | 10,2       | 1.0    |
| 30          | Coleta                    | Endereço de coleta da mercadoria                                                                                                                                                                                                                                                                                               | E     | Caractere | 1-0      | 250        | 1.0    |
| 31          | Entrega                   | Endereço de entrega da mercadoria                                                                                                                                                                                                                                                                                              | E     | Caractere | 1-0      | 250        | 1.0    |
| 32          | HabilitarDeclaracaoCiot   | Informar true para gerar CIOT com a EXTRATTA e false para não gerar o CIOT                                                                                                                                                                                                                                                     | E     | Booleano  | 1-1      | True/False | 1.0    |
| 33          | ForcarCiotNaoEquiparado   | False : Não gerar CIOT para ETC ou TAC não equiparado; True : Gerar CIOT para ETC ou TAC não equiparado;                                                                                                                                                                                                                       | E     | Booleano  | 1-1      | True/False | 1.0    |
| 34          | NaturezaCarga             | Informar o código nacional de natureza de carga. Utilizado para emissão do CIOT, conforme código ANTT.                                                                                                                                                                                                                         | E     | Inteiro   | 1-0      | 5          | 1.0    |
| 35          | CEPOrigem                 | CEP de Origem da Viagem. Caso não informado, será utilizado o CEP do Cliente de Origem.                                                                                                                                                                                                                                        | E     | Caractere | 1-0      | 8          | 1.0    |
| 36          | CEPDestino                | CEP de Destino da Viagem. Caso não informado, será utilizado o CEP do Cliente de Destino.                                                                                                                                                                                                                                      | E     | Caractere | 1-0      | 8          | 1.0    |
| 37          | CodigoTipoCarga           | Tabela com o código do tipo de carga. É possivel obter uma consultar atualizada através da API Viagem/ConsultarTiposCarga. Caso não informado, será utilizado como padrão 5 (Carga Geral).                                                                                                                                     | E     | Inteiro   | 1-0      | 2          | 1.0    |
| 38          | DistanciaViagem           | Distância da Viagem em KM. Caso não informada, será utilizado o valor 0.                                                                                                                                                                                                                                                       | E     | Inteiro   | 1-0      | 10         | 1.0    |
| 39          | CarretasViagemV2          | Caso cliente opere com carretas locadas, em nome de outros proprietarios, podem usar a estrutura informando dados do proprietario da carreta juntamente com seu RNTRC                                                                                                                                                          | A     |           | 1-0      |            | 1.0    |
| 39.1        | Placa                     | Placa da Carreta ( Se enviado nesta estrutura nao é necessário enviar em Carretas )                                                                                                                                                                                                                                            | E     | Caractere | 1-0      | 100        | 1.0    |
| 39.2        | RNTRC                     | RNTRC/ANTT do proprietario da viagem                                                                                                                                                                                                                                                                                           | E     | Caractere | 1-0      | 10         | 1.0    |
| 39.3        | CPFCNPJProprietario       | CPF/CNPJ do Proprietário                                                                                                                                                                                                                                                                                                       | E     | Caractere | 1-0      | 14         | 1.0    |
| 40          | DadosPagamento            | Dados bancários                                                                                                                                                                                                                                                                                                                | G     |           | 1-0      | -          | 1.0    |
| 40.1        | FormaPagamento            | Forma de Pagamento da Viagem. Caso não informado, será utilizado o valor 5 - Outros. Os atuais valores são: <br/>__1-__ Cartao;<br/>__2-__ ContaCorrente;<br/>__3-__ ContaPoupanca;<br/>__4-__ ContaPagament;,<br/>__5-__ Outros.;                                                                                             | E     | Inteiro   | 1-0      | 1          | 1.0    |
| 40.2        | CodigoBacen               | Código BACEN do banco para pagamento em conta (Forma de pagamento 2, 3 e 4)                                                                                                                                                                                                                                                    | E     | Caractere | 1-0      | 5          | 1.0    |
| 40.3        | Agencia                   | Agência do banco para pagamento em conta (Forma de pagamento 2, 3 e 4)                                                                                                                                                                                                                                                         | E     | Caractere | 1-0      | 10         | 1.0    |
| 40.4        | Conta                     | Conta do banco para pagamento em conta (Forma de pagamento 2, 3 e 4)                                                                                                                                                                                                                                                           | E     | Caractere | 1-0      | 10         | 1.0    |
| 41          | DadosANTT                 | Dados para ANTT                                                                                                                                                                                                                                                                                                                | G     |           | 1-0      | -          | 1.0    |
| 41.1        | AltoDesempenho            | Indicação se veículo é de alto desempenho                                                                                                                                                                                                                                                                                      | E     | Booleano  | 1-0      | True/False | 1.0    |
| 41.2        | DestinacaoComercial       | Indicação se destinação é comercial                                                                                                                                                                                                                                                                                            | E     | Booleano  | 1-0      | True/False | 1.0    |
| 41.3        | FreteRetorno              | Indicação se há frete de retorno                                                                                                                                                                                                                                                                                               | E     | Booleano  | 1-0      | True/False | 1.0    |
| 41.4        | CEPRetorno                | CEP do frete de retorno. Necessário informar caso o campo FreteRetorno esteja true.                                                                                                                                                                                                                                            | E     | Caractere | 1-0      | 8          | 1.0    |
| 41.5        | DistanciaRetorno          | Distância do frete de retorno em KM. Necessário informar caso o campo FreteRetorno esteja true.                                                                                                                                                                                                                                | E     | Inteiro   | 1-0      | 10         | 1.0    |
| 42          | ViagemRegra               | Objeto pai que contém uma lista de regras para a viagem (Referência na viagem)                                                                                                                                                                                                                                                 | G     |           | 1-1      |            | 1.0    |
| 42.1        | TaxaAntecipacao           | Taxa em porcentagem para cálculo da antecipação de protocolo                                                                                                                                                                                                                                                                   | E     | Decimal   | 1-1      | 4,4        | 1.0    |
| 42.2        | ToleranciaPeso            | Tolerância de peso em porcentagem para cálculo da quebra de mercadoria                                                                                                                                                                                                                                                         | E     | Decimal   | 1-1      | 4,4        | 1.0    |
| 42.3        | TarifaTonelada            | Tarifa por tonelada em reais para cálculo das quebras de tarifa e mercadoria                                                                                                                                                                                                                                                   | E     | Decimal   | 1-1      | 10,2       | 1.0    |
| 42.4        | TipoQuebraMercadoria      | Tipo da quebra de mercadoria. <br/>__0:__ Diferença;<br/>__1:__ Integral.                                                                                                                                                                                                                                                      | E     | Inteiro   | 1-1      | 10         | 1.0    |
| 43          | ViagemEventos             | Parcelas da Viagem                                                                                                                                                                                                                                                                                                             | G     |           | 1-0      | -          | 1.0    |
| 43.1        | TipoEvento                | Tipo do evento a ser pago na viagem. <br/>__0:__ Adiantamento;<br/>__1:__ Saldo;<br/>__2:__ Estadia;<br/>__3:__ RPA;<br/>__4:__ Tarifa ANTT;<br/>__5:__ Abastecimento.                                                                                                                                                         | E     | Inteiro   | 1-1      | 10         | 1.0    |
| 43.2        | ValorPagamento            | Valor do pagamento                                                                                                                                                                                                                                                                                                             | E     | Decimal   | 1-1      | 10,2       | 1.0    |
| 43.3        | Status                    | Status do evento de pagamento:<br/>__0:__ Aberto/Pendente;<br/>__1:__ Bloqueado;<br/>__2:__ Baixado (Efetivado/Liquidado);<br/>__3:__ Cancelado;<br/>__5:__ Agendado.                                                                                                                                                          | E     | Inteiro   | 1-1      | 10         | 1.0    |
| 43.4        | DataAgendamentoPagamento  | Data de agendamento para efetivação automático de evento                                                                                                                                                                                                                                                                       | E     | Data      | 1-0      | dd/MM/yy   | 1.0    |
| 43.5        | Instrucao                 | Instruções para os pagamentos                                                                                                                                                                                                                                                                                                  | E     | Caractere | 1-0      | 100        | 1.0    |
| 43.6        | ValorBruto                | Valor bruto do evento RPA                                                                                                                                                                                                                                                                                                      | E     | Decimal   | 1-0      | 10,2       | 1.0    |
| 43.7        | HabilitarPagamentoCartao  | Ao definir o parâmetro de pagamento como __true__, a transação será processada via Extratta (Cartão).<br/> Caso o proprietário não tenha um cartão vinculado, o sistema criará automaticamente uma conta virtual, na qual o valor será disponibilizado.<br/> Se o parâmetro pagamento for definido como __false__, dois cenários são possíveis:<br/>__1-__ O pagamento poderá ser registrado como depósito (transação não irá ocorrer pela Extratta);<br/>__2-__ O pagamento poderá ser realizado via PIX através da Extratta, desde que o cliente tenha contratado o serviço de pagamento via PIX e o proprietário tenha uma chave PIX válida cadastrada.    | E     | Booleano  | 1-1      | 1          | 1.0    |
| 43.8        | NumeroControle            | Número único para identificação da parcela. Não é possível integrar 2 eventos com o mesmo número controle                                                                                                                                                                                                                      | E     | Caractere | 1-0      | 300        | 1.0    |
| 44          | Pedágio                   | Agrupado de informações relacionadas a compra de Vale-pedágio Obrigatório (VPO) ou Pedágio                                                                                                                                                                                                                                     | G     |           | 1-0      |            | 1.0    |
| 44.1        | Fornecedor                | Parceiro fornecedor de pedágio para efetuar compra: <br/>__0:__ Desabilitado (padrão);<br/>__1:__ Moedeiro: Carga no cartão moedeiro Extratta (__Descontinuado pela ANTT__);<br/>__2:__ Via Fácil (Sem Parar); <br/>__3:__ Move Mais;<br/>__4:__ Veloe; <br/>__5:__ Tag Extratta;  <br/>__6:__ ConectCar; <br/>__7:__ Taggy Edenred.         | E     | Inteiro   | 1-0      | 10         | 1.0    |
| 44.2        | IdentificadorHistorico    | Identificador obtido na rotina de roteirização previamente (Viagem/ConsultarCustoPedagioRota). A roteirização é obrigatória para fornecedor de pedágio modalidade TAG O protocolo Recibo Vale Pedágio ANTT somente é retornado quando enviar esta TAG, mediante roteirização.                                                  | E     | Caractere | 1-0      | 100        | 1.0    |
| 44.3        | ValorPedágio              | Caso deseje realizar a compra de pedágio, informando um valor fixo. Neste caso não é realizado o processo de autenticação (recebimento do recibo de vale pedágio gerado pela ANTT). Enviar OU o valor OU o identificadorHistorico ( HASH)                                                                                      | E     | Decimal   | 1-0      | 10,2       | 1.0    |
| 44.4        | NomeRota                  | Envio do Nome/Descrição da Rota previamente cadastrada. Enviar OU identificador OU ValorPedágio OU NomeRota OU IdRotaModelo Para o sistema realizar o cálculo conforme os eixos enviados na viagem o veículo deve ser integrado como TipoContrato diferente de 4 (terceiro). O veículo deve ser integrado associado a empresa! | E     | Caractere | 1-0      | 40         | 1.0    |
| 44.5        | IdRotaModelo              | Envio do Código da Rota previamente cadastrada. Enviar OU identificador OU ValorPedágio OU NomeRota OU IdRotaModelo Para o sistema realizar o cálculo conforme os eixos enviados na viagem o veículo deve ser integrado como TipoContrato diferente de 4 (terceiro). O veículo deve ser integrado associado a empresa!         | E     | Inteiro   | 1-0      | 1          | 1.0    |
| 45          | DocumentosFiscais         | Objeto pai que contém uma lista dos documentos fiscais __<< Limitar envio em 10 documentos >>__                                                                                                                                                                                                                                | A     |           |          |            | 1.0    |
| 45.1        | NumeroDocumento           | Número do documento                                                                                                                                                                                                                                                                                                            | E     | Inteiro   | 1-0      | 10         | 1.0    |
| 45.2        | Serie                     | Número da série                                                                                                                                                                                                                                                                                                                | E     | Caractere | 1-0      | 4          | 1.0    |
| 45.3        | PesoSaida                 | Peso de saída                                                                                                                                                                                                                                                                                                                  | E     | Decimal   | 1-0      | 10,3       | 1.0    |
| 45.4        | Valor                     | Valor                                                                                                                                                                                                                                                                                                                          | E     | Decimal   | 1-0      | 10,2       | 1.0    |
| 45.5        | TipoDocumento             | Enum de tipo de documento, sendo eles: <br/>__0-__ Outros;<br/>__1-__ CTE;<br/>__2-__ NFServiço;<br/>__3-__ OrdemColeta;<br/>__4-__ NotaFiscal.                                                                                                                                                                                | E     | Inteiro   | 1-1      | 1          | 1.0    |
| 45.6        | Chave                     | Chave do Documento                                                                                                                                                                                                                                                                                                             | E     | Caractere | 1-0      | 150        | 1.0    |
| 46          | Token                     | Gerado a partir do CNPJ de autenticação para validar permissões de acesso                                                                                                                                                                                                                                                      | E     | Caractere | 1-1      | 100        | 1.0    |
| 47          | DocumentoUsuarioAudit     | Documento do usuário que está realizando a operação                                                                                                                                                                                                                                                                                                        | E     | Caractere | 1-1      | 14         | 1.0    |
| 48          | NomeUsuarioAudit          | Nome do usuário que está realizando a operação                                                                                                                                                                                                                                                                                                             | E     | Caractere | 1-1      | 60         | 1.0    |

<br>

## <b>Modelo de Json - Request</b>

<br>

```json
{
  "CNPJAplicacao": "",
  "CNPJEmpresa": "",
  "CPFCNPJClienteDestino": "",
  "CPFCNPJClienteOrigem": "",
  "CPFCNPJClienteTomador": "",
  "CPFCNPJProprietario": "",
  "NomeProprietario": "",
  "RNTRC": "",
  "CNPJFilial": "",
  "RazaoSocialFilial": "",
  "CPFMotorista": "",
  "Placa": "",
  "ForcarCiotNaoEquiparado": false,
  "DataColeta": "31/12/2024",
  "DataPrevisaoEntrega": "31/12/2024",
  "StatusViagem": 1,
  "StatusIntegracao": 2,
  "DocumentoCliente": "",
  "PesoSaida": 0.0,
  "ValorPedagio": 0.0,
  "PedagioBaixado": 0.0,
  "ValorMercadoria": 0.0,
  "IRRPF": 0.0,
  "INSS": 0.0,
  "SESTSENAT": 0.0,
  "NumeroDocumento": "",
  "DataEmissao": "",
  "Produto": "",
  "Unidade": "",
  "Quantidade": 0.0,
  "Coleta": "",
  "Entrega": "",
  "NumeroCartao": "",
  "HabilitarDeclaracaoCiot": true,
  "NaturezaCarga": 0,
  "NumeroControle": "",
  "CEPOrigem": "",
  "CEPDestino": "",
  "CodigoTipoCarga": 5,
  "DistanciaViagem": 0,
  "CarretasViagemV2": [
    {
      "Placa": "",
      "RNTRC": "",
      "CPFCNPJProprietario": ""
    }
  ],
  "DadosANTT": {
    "AltoDesempenho": false,
    "DestinacaoComercial": false,
    "FreteRetorno": false,
    "CEPRetorno": "",
    "DistanciaRetorno": 0
  },
  "ViagemRegra": [
    {
      "TaxaAntecipacao": 0.0,
      "ToleranciaPeso": 0.0,
      "TarifaTonelada": 1,
      "TipoQuebraMercadoria": 0
    }
  ],
  "ViagemEventos": [
    {
      "TipoEvento": 1,
      "ValorPagamento": 0.0,
      "Status": 0,
      "HabilitarPagamentoCartao": false,
      "NumeroControle": "",
      "Instrucao": ""
    }
  ],
  "Pedagio": {
    "Fornecedor": 0,
    "IdentificadorHistorico": "",
    "ValorPedagio": 0.0,
    "NomeRota": "",
    "IdRotaModelo": 0
  },
  "DocumentosFiscais": [
    {
      "NumeroDocumento": 0,
      "Serie": "",
      "PesoSaida": 0.0,
      "Valor": 0.0,
      "TipoDocumento": 0,
      "Chave": ""
    }
  ],
  "Token": "",
  "DocumentoUsuarioAudit": "",
  "NomeUsuarioAudit": ""
}
```

<br>

## <b>Response</b>

<br>

| Índice      | Chave                           | Descrição                                                                                                                                                                                                                        | Elem. | Tipo        | Ocor.   | Tam. | Versão |
|-------------|---------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------|-------------|---------|------|--------|
| 1           | Sucesso                         | __True:__ Caso não tenha acontecido nenhuma exceção ou validação no processo mesmo não trazendo dados no retorno; <br/>__False:__ Qualquer validação ou exceção não tratada pelo sistema que impediu o processo de ser concluído | E     | Booleano    | 1-1     |      | 1.0    |
| 2           | Mensagem                        | Mensagem de retorno                                                                                                                                                                                                              | E     | Caractere   | 1-1     | 200  | 1.0    |
| 3           | Object                          | Objeto pai que contém uma lista de eventos da viagem (parcelas)                                                                                                                                                                  | G     |             | 0-1     |      | 1.0    |
| 3.1         | IdViagem                        | Código da viagem – deve ser armazenado este Id para retificar/alterar a respectiva viagem                                                                                                                                        | E     | Inteiro     | 1-1     | 10   | 1.0    |
| 3.2         | NumeroDocumento                 | Numero do documento                                                                                                                                                                                                              | E     | Caractere   | 1-1     | 14   | 1.0    |
| 3.3         | IdsViagemEstabelecimento        | Lista de ids dos estabelecimentos da viagem                                                                                                                                                                                      | A     | Inteiro     | 0-1     | 10   | 1.0    |
| 3.4         | IRRPF                           | IRRPF da viagem                                                                                                                                                                                                                  | E     | Inteiro     | 0-1     | 10   | 1.0    |
| 3.5         | INSS                            | INSS da viagem                                                                                                                                                                                                                   | E     | Inteiro     | 0-1     | 10   | 1.0    |
| 3.6         | SESTSENAT                       | SEST/SENAT da viagem                                                                                                                                                                                                             | E     | Inteiro     | 0-1     | 10   | 1.0    |
| 3.7         | Eventos                         | Objeto pai que contém uma lista de eventos da viagem (parcelas)                                                                                                                                                                  | A     |             | 0-1     |      | 1.0    |
| 3.7.1       | OperacaoCartao                  | Informações referente ao sucesso quanto a carga de valor no cartão.                                                                                                                                                              | G     |             | 0-1     |      | 1.0    |
| 3.7.1.1     | Status                          | Enum referente Situação da transação realizada com o cartão do motorista, podendo ser: <br/>__0:__ Pendente;<br/>__1:__ Sucesso;<br/>__2:__ Erro;<br/>__3:__ NaoHabilitado.                                                      | E     | Inteiro     | 0-1     | 1    | 1.0    |
| 3.7.1.2     | Mensagem                        | Mensagem com indicador de falha do processo                                                                                                                                                                                      | E     | Caractere   | 0-1     | 200  | 1.0    |
| 3.7.2       | IdViagemEvento                  | Código do evento da viagem – deve ser armazenado este Id para retificar/alterar o respectivo evento                                                                                                                              | E     | Inteiro     | 0-1     | 10   | 1.0    |
| 3.7.3       | NumeroControle                  | Número de controle do evento para ERP                                                                                                                                                                                            | E     | Caractere   | 0-1     | 20   | 1.0    |
| 3.7.4       | Token                           | Token do evento da viagem                                                                                                                                                                                                        | E     | Caractere   | 1-1     | 100  | 1.0    |
| 3.7.5       | Instrucao                       | Instrução do evento da viagem/Observações                                                                                                                                                                                        | E     | Caractere   | 0-1     | 100  | 1.0    |
| 3.7.6       | IdsViagemOutrosDescontos        | Lista de códigos de outros descontos                                                                                                                                                                                             | A     | Inteiro     | 0-1     | 10   | 1.0    |
| 3.7.7       | TipoEventoViagem                | Tipo de evento de viagens: <br/>__0:__ Adiantamento;<br/>__1:__ Saldo;<br/>__2:__ Estadia;<br/>__3:__ RPA;<br/>__4:__ Tarifa ANTT;<br/>__5:__ Abastecimento                                                                      | E     | Inteiro     | 0-1     | 1    | 1.0    |
| 3.7.8       | ValorBruto                      | Valor bruto                                                                                                                                                                                                                      | E     | Decimal     | 0-1     | 10,2 | 1.0    |
| 3.7.9       | IdsViagemDocumento              | Lista de códigos dos documentos da viagem                                                                                                                                                                                        | A     | Inteiro     | 0-1     | 10   | 1.0    |
| 3.7.10      | ViagemOutrosDescontos           | Objeto pai que contém uma lista de outros descontos relacionados a um evento da viagem                                                                                                                                           | A     | Inteiro     | 0-1     | 1    | 1.0    |
| 3.7.10      | ViagemOutrosAcrescimos          | Objeto pai que contém uma lista de outros acréscimos relacionados a um evento da viagem                                                                                                                                          | A     | Inteiro     | 0-1     | 1    | 1.0    |
| 3.8         | CIOT                            | Grupo de informações relacionadas ao CIOT da viagem integrada                                                                                                                                                                    | G     |             | 0-1     |      | 1.0    |
| 3.8.1       | Declarado                       | Indica que a viagem foi gerada com o respectivo CIOT                                                                                                                                                                             | E     | Booleano    | 1-1     | True/False | 1.0    |
| 3.8.2       | Resultado                       | Enum que indica o status da declaração de CIOT, podendo ser:  <br/>__0:__ Sucesso;<br/>__1:__ Erro;<br/>__2:__ NaoObrigatorio;<br/>__3:__ NaoHabilitado.                                                                         | E     | Inteiro     | 1-1     | 1    | 1.0    |
| 3.8.3       | Mensagem                        | Mensagem informativa sobre a declaração do CIOT                                                                                                                                                                                  | E     | Caractere   | 0-1     | 200  | 1.0    |
| 3.8.4       | Dados                           | Grupo de informações com os dados integrados na ANTT referente ao CIOT                                                                                                                                                           | G     |             | 0-1     |      | 1.0    |
| 3.8.4.1     | Ciot                            | Número do CIOT gerado pelo meio homologado                                                                                                                                                                                       | E     | Caractere   | 1-1     | 12   | 1.0    |
| 3.8.4.2     | Verificador                     | Número verificador do CIOT gerado pela ANTT                                                                                                                                                                                      | E     | Caractere   | 1-1     | 4    | 1.0    |
| 3.8.4.3     | Senha                           | Senha de acesso ao CIOT gerada pelo meio homologado                                                                                                                                                                              | E     | Caractere   | 1-1     | 20   | 1.0    |
| 3.8.4.4     | EmContigencia                   | Informativo verdadeiro/falso indicando se a viagem foi declarada em contingência na ANTT                                                                                                                                         | E     | Booleano    | 1-1     | True/False | 1.0    |
| 3.8.4.5     | DataDeclaracao                  | Data e hora da integração com ANTT                                                                                                                                                                                               | E     | Caractere   | 1-1      | yyyy-MM-dd HH:mm:ss | 1.0    |
| 3.8.4.6     | AvisoTransportador              | Mensagem de aviso cadastrada pela ANTT para o contratado                                                                                                                                                                         | E     | Caractere   | 0-1     | 300  | 1.0    |
| 3.8.5       | DadosANTT                       | Dados para ANTT                                                                                                                                                                                                                  | G     |             | 0-1     |      | 1.0    |
| 3.8.5.1     | AltoDesempenho                  | Indicação se veículo é de alto desempenho                                                                                                                                                                                        | E     | Booleano    | 0-1      | True/False | 1.0    |
| 3.8.5.2     | DestinacaoComercial             | Indicação se destinação é comercial                                                                                                                                                                                              | E     | Booleano    | 0-1      | True/False | 1.0    |
| 3.8.5.3     | FreteRetorno                    | Indicação se há frete de retorno                                                                                                                                                                                                 | E     | Booleano    | 0-1      | True/False | 1.0    |
| 3.8.5.4     | CEPRetorno                      | CEP do frete de retorno. Necessário informar caso o campo FreteRetorno esteja true.                                                                                                                                              | E     | Caractere   | 0-1      | 8          | 1.0    |
| 3.8.5.5     | DistanciaRetorno                | Distância do frete de retorno em KM. Necessário informar caso o campo FreteRetorno esteja true.                                                                                                                                  | E     | Inteiro     | 0-1      | 10         | 1.0    |
| 3.9         | Pedagio                         | Grupo de informação relacionadas a compra do pedágio caso habilitada                                                                                                                                                             | G     |             | 0-1     | 1    | 1.0    |
| 3.9.1       | Status                          | Enum indicador de sucesso na comunicação com fornecedor de pedágio, podendo ser eles: <br/> __0 -__ CompraSolicitada; <br/> __1 -__ Erro;<br/> __2 -__ NaoRealizado;<br/> __3 -__ CancelamentoSolicitado;<br/> __4 -__ CompraConfirmada;<br/> __5 -__ CancelamentoConfirmado. | E     | Inteiro     | 1-1     | 1    | 1.0    |
| 3.9.2       | Mensagem                        | Mensagem com indicador da mensagem de falha do processo                                                                                                                                                                          | E     | Caractere   | 0-1     | 200  | 1.0    |
| 3.9.3       | Valor                           | Valor solicitado para carga de pedágio                                                                                                                                                                                           | E     | Decimal     | 1-1     | 18,2 | 1.0    |
| 3.9.4       | ProtocoloRequisicao             | Número de protocolo do registro de requisição de pedágio para ANTT                                                                                                                                                               | E     | Caractere   | 0-1     | 30   | 1.0    |
| 3.9.5       | ProtocoloProcessamento          | Número de protocolo do registro de processamento do pedágio na ANTT                                                                                                                                                              | E     | Caractere   | 0-1     | 30   | 1.0    |
| 3.9.6       | EstornoSaldoResidualSolicitado  | Existe estorno, sim ou nao                                                                                                                                                                                                       | E     | Booleano    | 0-1     | True/False | 1.0    |
| 3.9.7       | ProtocoloValePedagio            | Número de protocolo do registro de pedágio na ANTT                                                                                                                                                                               | E     | Caractere   | 0-1     | 30   | 1.0    |
| 3.9.8       | ProtocoloEnvioValePedagio       | Número de protocolo do registro de envio do pedágio para ANTT. <br/> __Deve ser informado no MDF-e o campo sem os 8 primeiros dígitos que identificam a Fornecedora de Vale-Pedágio obrigatório__;                               | E     | Caractere   | 0-1     | 30   | 1.0    |
| 3.9.9       | AvisoTransportador              | Mensagem de aviso                                                                                                                                                                                                                | E     | Caractere   | 0-1     | 30   | 1.0    |
| 3.9.10      | Fornecedor                      | Parceiro fornecedor de pedágio para efetuar compra: <br/>__0:__ Desabilitado (padrão);<br/>__1:__ Moedeiro: Carga no cartão moedeiro Extratta (__Descontinuado pela ANTT__);<br/>__2:__ Via Fácil (Sem Parar); <br/>__3:__ Move Mais;<br/>__4:__ Veloe; <br/>__5:__ Tag Extratta;  <br/>__6:__ ConectCar; <br/>__7:__ Taggy Edenred.  | E     | Inteiro   | 0-1     | 1    | 1.0    |
| 3.9.11      | CNPJFornecedor                  | CNPJ do Fornecedor de Vale Pedagio                                                                                                                                                                                               | E     | Caractere   | 0-1     | 20    | 1.0    |
| 3.9.12      | CompraCredenciaisExtratta       | Se a compra do Vale Pedagio foi feita com as credenciais da extratta                                                                                                                                                             | E     | Booleano    | 0-1     | True/False  1.0    |
| 4           | Faults                          | Objeto pai que contém uma lista de erro(s) ou aviso(s) caso aconteça                                                                                                                                                             | A     |             |         |       |        |
| 4.1         | Type                            | Tipo de erro ou aviso, podendo ser eles: <br/> __1 -__ Error; <br/> __2 -__ Alert.                                                                                                                                               | E     | Inteiro     | 0-1     | 1     | 1.0    |
| 4.2         | Code                            | Codigo do erro                                                                                                                                                                                                                   | E     | Caractere   | 0-1     | 10    | 1.0    |
| 4.3         | Message                         | Mensagem do erro                                                                                                                                                                                                                 | E     | Caractere   | 0-1     | 200   | 1.0    |

<br>

## <b>Modelo de Json - Response</b>

<br>

```json
{
  "Sucesso": true,
  "Mensagem": null,
  "Objeto": {
    "IdViagem": 0,
    "NumeroDocumento": "",
    "IdsViagemEstabelecimento": [],
    "Eventos": [
      {
        "OperacaoCartao": {
          "Status": 0,
          "Mensagem": ""
        },
        "IdViagemEvento": ,
        "NumeroControle": "",
        "Token": "",
        "IdsViagemDocumento": [],
        "IdsViagemVlAdicional": null,
        "IdsViagemOutrosDescontos": [],
        "ViagemOutrosDescontos": [],
        "IdsViagemOutrosAcrescimos": [],
        "ViagemOutrosAcrescimos": [],
        "TipoEventoViagem": 1,
        "ValorBruto": 0.0
      }
    ],
    "CIOT": {
      "Resultado": 0,
      "Declarado": true,
      "Mensagem": null,
      "Dados": {
        "Ciot": "",
        "Verificador": "",
        "Senha": "",
        "EmContigencia": false,
        "DataDeclaracaoDateTime": "",
        "DataDeclaracao": "",
        "AvisoTransportador": null
      },
      "DadosAntt": {
        "AltoDesempenho ": false,
        "DestinacaoComercial": false,
        "FreteRetorno": false,
        "CEPRetorno": "",
        "DistanciaRetorno": 0

      }
    },
    "Pedagio": {
      "Status": 0,
      "Mensagem": "",
      "Valor": 0.0,
      "ProtocoloRequisicao": "",
      "ProtocoloProcessamento": "",
      "EstornoSaldoResidualSolicitado": false,
      "ProtocoloValePedagio": "",
      "ProtocoloEnvioValePedagio": "",
      "AvisoTransportador": null,
      "Fornecedor": 0,
      "CnpjFornecedor": "",
      "CompraCredenciaisExtratta": false
    },
    "IRRPF": 0.0,
    "INSS": 0.0,
    "SESTSENAT": 0.0,
    "Avisos": null
  },
   "Faults": [
    {
        "Type": 0,
        "Code": "",
        "Message": ""
    }
  ]
}
```