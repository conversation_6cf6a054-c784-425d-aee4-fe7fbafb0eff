<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Integração de Abastecimento - TicketLog e Shell Expers</title>
    <style>
        @page {
            margin: 2cm;
            size: A4;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 100%;
            margin: 0;
            padding: 0;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        h1 {
            color: #2c5aa0;
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .subtitle {
            color: #666;
            font-size: 16px;
            font-style: italic;
            margin-bottom: 20px;
        }
        
        h2 {
            color: #2c5aa0;
            font-size: 22px;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #2c5aa0;
            padding-left: 15px;
        }
        
        h3 {
            color: #1a365d;
            font-size: 18px;
            margin-top: 25px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        h4 {
            color: #2d3748;
            font-size: 16px;
            margin-top: 20px;
            margin-bottom: 8px;
            font-weight: bold;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 14px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #2c5aa0;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .code-block {
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        
        .endpoint {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 10px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        ul, ol {
            margin: 10px 0;
            padding-left: 25px;
        }
        
        li {
            margin: 5px 0;
        }
        
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #2c5aa0;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        
        .checklist {
            list-style-type: none;
            padding-left: 0;
        }
        
        .checklist li {
            margin: 8px 0;
            padding-left: 25px;
            position: relative;
        }
        
        .checklist li:before {
            content: "☐";
            position: absolute;
            left: 0;
            font-size: 16px;
            color: #2c5aa0;
        }
        
        .strong {
            font-weight: bold;
            color: #2c5aa0;
        }
        
        .center {
            text-align: center;
        }
        
        @media print {
            body {
                font-size: 12px;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            .code-block {
                font-size: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Integração de Abastecimento</h1>
        <h1>TicketLog e Shell Expers</h1>
        <div class="subtitle">
            Guia completo para integração de solicitação e cancelamento de crédito de abastecimento<br>
            utilizando a estrutura atual da integração de viagem (viagem/integrar)
        </div>
    </div>

    <h2>Visão Geral</h2>
    <p>A funcionalidade de abastecimento utiliza a estrutura existente da API de viagem, adicionando campos específicos para gerenciar créditos de combustível com os fornecedores:</p>
    <ul>
        <li><span class="strong">TicketLog</span> (Fornecedor = 0)</li>
        <li><span class="strong">Shell Expers</span> (Fornecedor = 1)</li>
    </ul>

    <h2>Regras de Negócio</h2>
    
    <h3>Solicitação de Crédito</h3>
    <ul>
        <li>Utiliza parcelas <span class="strong">novas</span> (IdViagemEvento = NULL)</li>
        <li>TipoEvento = 5 (ABASTECIMENTO)</li>
        <li>Status = 2 (Baixado) para realizar o crédito</li>
        <li>HabilitarPagamentoCartao = false (sempre)</li>
        <li>O processo comprará o crédito no fornecedor, <span class="strong">não fará pagamento PIX</span></li>
    </ul>

    <h3>Retificação de Viagem</h3>
    <ul>
        <li>Em retificações de viagem já cadastrada, <span class="strong">NÃO</span> enviar novamente a parcela de ABASTECIMENTO</li>
        <li>Evita duplicação de solicitações</li>
    </ul>

    <h3>Alteração de Parcela</h3>
    <p>Para alterar uma parcela de abastecimento existente:</p>
    <ol>
        <li>Enviar cancelamento da parcela atual (baseado no IdViagemEvento)</li>
        <li>Enviar nova parcela com os dados atualizados</li>
    </ol>

    <h3>Cancelamento de Crédito</h3>
    <ul>
        <li>Premissa: deve existir uma parcela com solicitação de crédito já enviada</li>
        <li>Status = 3 (Cancelado)</li>
        <li>Informar IdViagemEvento da parcela a ser cancelada</li>
    </ul>

    <h3>Placa do Veículo</h3>
    <p>A placa enviada para TicketLog ou Shell Expers será o campo "Placa" do JSON da Viagem</p>

    <div class="page-break"></div>

    <h2>Estrutura de Dados</h2>

    <h3>Campos Principais - ViagemEventos</h3>
    <table>
        <thead>
            <tr>
                <th>Campo</th>
                <th>Descrição</th>
                <th>Valor para Abastecimento</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>IdViagemEvento</td>
                <td>ID do evento da viagem</td>
                <td>NULL para parcelas novas</td>
            </tr>
            <tr>
                <td>ValorPagamento</td>
                <td>Valor do pagamento</td>
                <td>Valor do crédito para TicketLog/Shell Expers</td>
            </tr>
            <tr>
                <td>Status</td>
                <td>Status do evento</td>
                <td>2 = Solicitar crédito; 3 = Cancelar crédito</td>
            </tr>
            <tr>
                <td>TipoEvento</td>
                <td>Tipo de parcela</td>
                <td>5 = ABASTECIMENTO</td>
            </tr>
            <tr>
                <td>Instrucao</td>
                <td>Informação adicional</td>
                <td>"Compra Crédito Abastecimento"</td>
            </tr>
            <tr>
                <td>HabilitarPagamentoCartao</td>
                <td>Habilitar pagamento cartão</td>
                <td>false (sempre)</td>
            </tr>
            <tr>
                <td>NumeroControle</td>
                <td>Documento controle</td>
                <td>Documento de controle</td>
            </tr>
        </tbody>
    </table>

    <h3>Estrutura DadosAbastecimento</h3>
    <table>
        <thead>
            <tr>
                <th>Campo</th>
                <th>Descrição</th>
                <th>Obrigatório</th>
                <th>Tipo</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>CodigoCredito</td>
                <td>Código do crédito na TicketLog/Shell Expers</td>
                <td>Não</td>
                <td>String</td>
            </tr>
            <tr>
                <td>CodigoCliente</td>
                <td>Código do cliente (obtido automaticamente do cadastro de empresa)</td>
                <td>Não</td>
                <td>String</td>
            </tr>
            <tr>
                <td>CodigoProduto</td>
                <td>Código do produto (deve ser enviado pelo TMS)</td>
                <td><span class="strong">Sim</span></td>
                <td>String</td>
            </tr>
            <tr>
                <td>numerocartao</td>
                <td>Número do cartão na TicketLog/Shell Expers</td>
                <td>Não</td>
                <td>String</td>
            </tr>
            <tr>
                <td>DataValidade</td>
                <td>Data de validade (formato yyyy-MM-dd)</td>
                <td>Não</td>
                <td>Date</td>
            </tr>
            <tr>
                <td>DataLiberacao</td>
                <td>Data de liberação (formato yyyy-MM-dd)</td>
                <td>Não</td>
                <td>Date</td>
            </tr>
            <tr>
                <td>Fornecedor</td>
                <td>0 = TicketLog (padrão); 1 = Shell Expers</td>
                <td>Não*</td>
                <td>Integer</td>
            </tr>
        </tbody>
    </table>

    <p><span class="strong">Observação:</span> *Fornecedor é opcional. Se não informado, assume TicketLog (0). Enviar 1 apenas para Shell Expers.</p>
        </tbody>
    </table>

    <div class="page-break"></div>

    <h2>Exemplos de Implementação</h2>

    <h3>V1 - Solicitação de Crédito</h3>
    <div class="endpoint">Endpoint: POST /Viagem/Integrar</div>
    <div class="code-block">{
  "CNPJAplicacao": "12345678000195",
  "CNPJEmpresa": "98765432000198",
  "CPFCNPJClienteDestino": "11111111000111",
  "CPFCNPJClienteOrigem": "22222222000222",
  "CPFCNPJProprietario": "33333333000333",
  "CPFMotorista": "12345678901",
  "Placa": "ABC1234",
  "NumeroControle": "CTRL001",
  "ViagemEventos": [
    {
      "TipoEvento": 5,
      "ValorPagamento": 500.00,
      "Status": 2,
      "HabilitarPagamentoCartao": false,
      "NumeroControle": "DOC123456",
      "Instrucao": "Compra Crédito Abastecimento",
      "DadosAbastecimento": {
        "CodigoCredito": "",
        "CodigoCliente": "CLI001",
        "CodigoProduto": "DIESEL_S10",
        "numerocartao": "1234567890123456",
        "DataValidade": "2024-12-31",
        "DataLiberacao": "2024-01-15",
        "Fornecedor": 0
      }
    }
  ]
}</div>

    <h3>V2 - Solicitação de Crédito</h3>
    <div class="endpoint">Endpoint: POST /ViagemV2/Integrar</div>
    <div class="code-block">{
  "Token": "token-autenticacao",
  "CnpjAplicacao": "12345678000195",
  "CNPJEmpresa": "98765432000198",
  "DocumentoUsuarioAudit": "12345678901",
  "NomeUsuarioAudit": "Usuario Sistema",
  "DadosViagem": {
    "DadosIniciais": {
      "NumeroControle": "CTRL001",
      "DeclaracaoCiot": 0
    },
    "Documentos": {
      "MotoristaDocumento": "12345678901",
      "ProprietarioDocumento": "33333333000333"
    },
    "Veiculo": {
      "Placa": "ABC1234"
    },
    "ViagemEventos": [
      {
        "IdViagemEvento": null,
        "ValorPagamento": 500.00,
        "Status": 2,
        "TipoEvento": 5,
        "Instrucao": "Compra Crédito Abastecimento",
        "HabilitarPagamentoCartao": false,
        "NumeroControle": "DOC123456",
        "DadosAbastecimento": {
          "CodigoCredito": "",
          "CodigoCliente": "CLI001",
          "CodigoProduto": "DIESEL_S10",
          "numerocartao": "1234567890123456",
          "DataValidade": "2024-12-31",
          "DataLiberacao": "2024-01-15",
          "Fornecedor": 0
        }
      }
    ]
  }
}</div>

    <div class="page-break"></div>

    <h3>Cancelamento de Crédito (V1 e V2)</h3>
    <div class="endpoint">Endpoint: POST /Viagem/Integrar ou POST /ViagemV2/Integrar</div>
    <div class="code-block">{
  "IdViagem": 12345,
  "CNPJAplicacao": "12345678000195",
  "Token": "token-autenticacao",
  "CNPJEmpresa": "98765432000198",
  "ViagemEventos": [
    {
      "IdViagemEvento": 67890,
      "Status": 3,
      "NumeroControle": "CREDITO123",
      "DadosAbastecimento": {
        "CodigoCliente": "CLI001",
        "CodigoProduto": "DIESEL_S10"
      }
    }
  ]
}</div>

    <h2>Fluxo de Integração</h2>

    <h3>1. Solicitação de Crédito</h3>
    <ol>
        <li>Preparar dados da viagem com TipoEvento = 5</li>
        <li>Definir Status = 2 (Baixado)</li>
        <li>Preencher DadosAbastecimento com informações do fornecedor</li>
        <li>Enviar IdViagemEvento = null para parcelas novas</li>
        <li>Definir HabilitarPagamentoCartao = false</li>
        <li>Chamar endpoint de integração de viagem</li>
    </ol>

    <h3>2. Cancelamento de Crédito</h3>
    <ol>
        <li>Obter IdViagemEvento da parcela a ser cancelada</li>
        <li>Preparar requisição com Status = 3 (Cancelado)</li>
        <li>Incluir campos obrigatórios: NumeroControle, CodigoCliente, CodigoProduto</li>
        <li>Chamar endpoint de integração de viagem</li>
    </ol>

    <h3>3. Alteração de Crédito</h3>
    <ol>
        <li>Cancelar parcela existente (Status = 3)</li>
        <li>Criar nova parcela com dados atualizados (Status = 2)</li>
    </ol>

    <div class="highlight info">
        <h4>Observações Importantes</h4>
        <ul>
            <li><span class="strong">CodigoProduto</span>: Deve ser enviado pelo TMS conforme especificação do fornecedor</li>
            <li><span class="strong">CodigoCliente</span>: Será obtido automaticamente do cadastro de empresa</li>
            <li><span class="strong">Placa</span>: Enviada automaticamente baseada no campo "Placa" da viagem</li>
            <li><span class="strong">Datas</span>: Usar formato yyyy-MM-dd</li>
            <li><span class="strong">Duplicação</span>: Não reenviar parcelas de abastecimento em retificações</li>
            <li><span class="strong">Fornecedores</span>: 0 = TicketLog; 1 = Shell Expers</li>
        </ul>
    </div>

    <div class="page-break"></div>

    <h2>Códigos de Status</h2>
    <table>
        <thead>
            <tr>
                <th>Status</th>
                <th>Descrição</th>
                <th>Uso</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>0</td>
                <td>Aberto/Pendente</td>
                <td>Parcela criada mas não processada</td>
            </tr>
            <tr>
                <td>1</td>
                <td>Bloqueado</td>
                <td>Parcela bloqueada</td>
            </tr>
            <tr>
                <td>2</td>
                <td>Baixado</td>
                <td><span class="strong">Solicitar crédito de abastecimento</span></td>
            </tr>
            <tr>
                <td>3</td>
                <td>Cancelado</td>
                <td><span class="strong">Cancelar crédito de abastecimento</span></td>
            </tr>
            <tr>
                <td>5</td>
                <td>Agendado</td>
                <td>Parcela agendada</td>
            </tr>
        </tbody>
    </table>

    <h2>Fornecedores Suportados</h2>
    <table>
        <thead>
            <tr>
                <th>Código</th>
                <th>Fornecedor</th>
                <th>Descrição</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>0</td>
                <td>TicketLog</td>
                <td>Fornecedor TicketLog</td>
            </tr>
            <tr>
                <td>1</td>
                <td>Shell Expers</td>
                <td>Fornecedor Shell Expers</td>
            </tr>
        </tbody>
    </table>

    <div class="page-break"></div>

    <h2>Resposta da API</h2>

    <h3>Sucesso - Solicitação de Crédito</h3>
    <div class="code-block">{
  "Sucesso": true,
  "Mensagem": "Viagem integrada com sucesso",
  "Objeto": {
    "IdViagem": 12345,
    "NumeroDocumento": "DOC123456",
    "Eventos": [
      {
        "IdViagemEvento": 67890,
        "NumeroControle": "DOC123456",
        "Token": "evento-token-123",
        "TipoEventoViagem": 5,
        "OperacaoCartao": {
          "Status": 1,
          "Mensagem": "Crédito solicitado com sucesso"
        }
      }
    ]
  }
}</div>

    <h3>Sucesso - Cancelamento de Crédito</h3>
    <div class="code-block">{
  "Sucesso": true,
  "Mensagem": "Crédito cancelado com sucesso",
  "Objeto": {
    "IdViagem": 12345,
    "Eventos": [
      {
        "IdViagemEvento": 67890,
        "TipoEventoViagem": 5,
        "OperacaoCartao": {
          "Status": 1,
          "Mensagem": "Cancelamento processado"
        }
      }
    ]
  }
}</div>

    <h2>Tratamento de Erros</h2>

    <h3>Erros Comuns</h3>
    <table>
        <thead>
            <tr>
                <th>Erro</th>
                <th>Causa</th>
                <th>Solução</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>"CodigoProduto obrigatório"</td>
                <td>Campo CodigoProduto não informado</td>
                <td>Informar código do produto conforme fornecedor</td>
            </tr>
            <tr>
                <td>"Fornecedor inválido"</td>
                <td>Valor de Fornecedor diferente de 0 ou 1</td>
                <td>Usar 0 para TicketLog ou 1 para Shell Expers</td>
            </tr>
            <tr>
                <td>"IdViagemEvento não encontrado"</td>
                <td>ID informado para cancelamento não existe</td>
                <td>Verificar ID correto da parcela</td>
            </tr>
            <tr>
                <td>"Parcela já cancelada"</td>
                <td>Tentativa de cancelar parcela já cancelada</td>
                <td>Verificar status atual da parcela</td>
            </tr>
            <tr>
                <td>"TipoEvento inválido"</td>
                <td>TipoEvento diferente de 5 para abastecimento</td>
                <td>Usar TipoEvento = 5</td>
            </tr>
        </tbody>
    </table>

    <h3>Exemplo de Resposta com Erro</h3>
    <div class="code-block">{
  "Sucesso": false,
  "Mensagem": "Erro na validação dos dados",
  "Faults": [
    {
      "Type": 1,
      "Code": "ABAST001",
      "Message": "CodigoProduto é obrigatório para abastecimento"
    }
  ]
}</div>

    <div class="page-break"></div>

    <h2>Checklist de Implementação</h2>

    <h3>Antes de Implementar</h3>
    <ul class="checklist">
        <li>Obter credenciais de acesso à API Extratta</li>
        <li>Configurar ambiente de homologação</li>
        <li>Obter códigos de produto dos fornecedores (TicketLog/Shell Expers)</li>
        <li>Configurar cadastro de empresa com códigos de cliente</li>
    </ul>

    <h3>Durante a Implementação</h3>
    <ul class="checklist">
        <li>Implementar estrutura DadosAbastecimento</li>
        <li>Validar campos obrigatórios (CodigoProduto, Fornecedor)</li>
        <li>Implementar lógica de Status (2 = Solicitar, 3 = Cancelar)</li>
        <li>Tratar IdViagemEvento = null para parcelas novas</li>
        <li>Implementar tratamento de erros</li>
    </ul>

    <h3>Testes</h3>
    <ul class="checklist">
        <li>Testar solicitação de crédito TicketLog (Fornecedor = 0)</li>
        <li>Testar solicitação de crédito Shell Expers (Fornecedor = 1)</li>
        <li>Testar cancelamento de crédito</li>
        <li>Testar alteração de parcela (cancelar + criar nova)</li>
        <li>Testar validações de campos obrigatórios</li>
        <li>Testar cenários de erro</li>
    </ul>

    <h2>Ambientes</h2>

    <h3>Homologação</h3>
    <ul>
        <li><span class="strong">URL</span>: http://apiho.extratta.com.br:50063/</li>
        <li><span class="strong">Finalidade</span>: Testes e validação da integração</li>
    </ul>

    <h3>Produção</h3>
    <ul>
        <li><span class="strong">URL</span>: https://api.extratta.com.br:2002/</li>
        <li><span class="strong">Finalidade</span>: Operação em ambiente real</li>
    </ul>

    <h2>Suporte</h2>
    <p>Para dúvidas técnicas ou problemas na integração:</p>
    <ul>
        <li>Consulte a documentação completa da API Extratta</li>
        <li>Entre em contato com o suporte técnico</li>
        <li>Utilize o ambiente de homologação para testes</li>
    </ul>

    <div class="footer">
        <p><strong>Nota:</strong> Esta funcionalidade estende a API existente de viagem sem modificar a estrutura principal, mantendo compatibilidade com integrações existentes.</p>
        <p>Documento gerado em: <span id="currentDate"></span></p>
    </div>

    <script>
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('pt-BR');
    </script>
</body>
</html>
