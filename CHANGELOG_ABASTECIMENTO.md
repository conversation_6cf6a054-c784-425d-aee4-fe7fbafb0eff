# Changelog - Implementação de Abastecimento

## Resumo das Alterações

Este documento descreve as alterações implementadas na documentação da API Extratta para suportar solicitação e cancelamento de crédito de abastecimento com fornecedores TicketLog e Shell Expers.

## Arquivos Modificados

### 1. `src/docs/Integrar_Viagem_V2.md`
- ✅ Adicionada nova seção "Regras para Abastecimento"
- ✅ Adicionado campo `DadosAbastecimento` na especificação de ViagemEventos
- ✅ Atualizada descrição do campo Status para incluir funcionalidades de abastecimento
- ✅ Adicionados exemplos específicos de solicitação e cancelamento de crédito
- ✅ Adicionado suporte na lista de funcionalidades
- ✅ Atualizada tabela de especificação com descrições detalhadas dos campos
- ✅ Adicionado campo IdViagemEvento na especificação e exemplos

### 2. `src/docs/Integrar_Viagem.md`
- ✅ Adicionada nova seção "Regras para Abastecimento"
- ✅ Adicionado campo `DadosAbastecimento` na especificação de ViagemEventos
- ✅ Adicionados exemplos específicos de solicitação e cancelamento de crédito
- ✅ Adicionado suporte na lista de funcionalidades
- ✅ Atualizada tabela de especificação com descrições detalhadas dos campos
- ✅ Atualizada descrição dos campos IdViagemEvento, NumeroControle e Instrucao

### 3. `src/docs/Atualizar_Viagem_V2.md`
- ✅ Adicionado campo `DadosAbastecimento` na especificação de ViagemEventos
- ✅ Adicionado exemplo JSON com estrutura DadosAbastecimento
- ✅ Atualizada tabela de especificação com descrições detalhadas dos campos

### 4. `src/docs/Atualizar_Viagem.md`
- ✅ Adicionado campo `DadosAbastecimento` na especificação de ViagemEventos
- ✅ Adicionado exemplo JSON com estrutura DadosAbastecimento
- ✅ Atualizada tabela de especificação com descrições detalhadas dos campos

### 5. `src/docs/Baixar_Evento.md`
- ✅ Atualizada descrição do campo Status para incluir funcionalidades de abastecimento

## Estrutura DadosAbastecimento Implementada

```json
{
  "DadosAbastecimento": {
    "CodigoCredito": "",        // Código do crédito (opcional)
    "CodigoCliente": "",        // Código do cliente no fornecedor (opcional)
    "CodigoProduto": "",        // Código do produto (obrigatório)
    "numerocartao": "",         // Número do cartão (opcional)
    "DataValidade": "2024-12-31", // Data de validade (opcional)
    "DataLiberacao": "2024-12-31", // Data de liberação (opcional)
    "Fornecedor": 0             // 0 = TicketLog, 1 = Shell Expers (obrigatório)
  }
}
```

## Regras de Negócio Implementadas

### Solicitação de Crédito
- TipoEvento = 5 (Abastecimento)
- Status = 2 (Baixado) para solicitar crédito
- HabilitarPagamentoCartao = false (sempre)
- IdViagemEvento = null para parcelas novas
- ValorPagamento = valor do crédito para TicketLog ou Shell Expers
- Instrucao = "Compra Crédito Abastecimento" (sugerido)
- NumeroControle = documento de controle
- Processo comprará crédito no fornecedor, não realizará pagamento PIX

### Cancelamento de Crédito
- Status = 3 (Cancelado) para cancelar crédito
- Necessário IdViagemEvento da parcela existente
- Campos obrigatórios: NumeroControle, CodigoCliente, CodigoProduto
- NumeroControle = CodigoCredito na TicketLog ou Shell Expers

### Fornecedores Suportados
- 0 = TicketLog
- 1 = Shell Expers

### Observações Importantes
- Placa do veículo é enviada automaticamente baseada no campo "Placa" da viagem
- CodigoProduto deve ser enviado pelo TMS conforme especificação do fornecedor
- CodigoCliente será obtido do cadastro de empresa
- Em retificações de viagem, não reenviar parcela de ABASTECIMENTO para evitar duplicação
- Para alterar parcela existente: cancelar a atual e criar nova
- Campos de data devem usar formato yyyy-MM-dd

## Status da Implementação

✅ **CONCLUÍDO** - Todas as alterações foram implementadas com sucesso nos arquivos de documentação da API Extratta V1 e V2.

## Próximos Passos

1. Revisar a documentação implementada
2. Validar exemplos JSON
3. Testar integração com fornecedores TicketLog e Shell Expers
4. Atualizar documentação adicional se necessário
