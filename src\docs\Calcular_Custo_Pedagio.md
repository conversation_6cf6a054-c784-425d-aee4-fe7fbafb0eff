<h1><b>R<PERSON>irização de Pedágio</b></h1>

<br> <h4> Calcular valor total do pedágio para rota. Cada processamento resulta em um código de histórico único,
e este deve ser utilizado no processo de integração de viagem caso desejar efetuar a compra do pedágio. </h4>

<b>Tipos de Rota:</b>

+ <PERSON><PERSON> curta (THE_SHORTEST): Vai calcular a rota pela distancia de KmTotal mais curta.
+ Mais Rapida (THE_FASTEST): Vai calcular a rota pelo TempoPrevisto mais rapido.

Quando nao informado o campo TipoRota, o valor padrao utilizado para calcular a rota é mais rapido (THE_FASTEST).

<br>

## <b>Request</b>

<br>

   * __ENDPOINT: Viagem/ConsultarCustoPedagioRota__
   * __VERBO: POST__

<br>

| Índice      | Chave             | Descrição                                                                                                                                                                                                                                                                                                                                                                                                                                           | Elem. | Tipo      | Ocor. | Tamanho | Versão |
|-------------|-------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------|-----------|-------|---------|--------|
| 1           | CNPJAplicacao     | CNPJ da Aplicação Para autenticação                                                                                                                                                                                                                                                                                                                                                                                                                 | E     | Caractere | 1-1   | 14      | 1.0    |
| 2           | Token             | Gerado a partir do CNPJ de autenticação para validar permissões de acesso                                                                                                                                                                                                                                                                                                                                                                           | E     | Caractere | 1-1   | 100     | 1.0    |
| 3           | DocumentoUsuarioAudit | Documento do usuário que está realizando a operação                                                                                                                                                                                                                                                                                                                                                                                              | E     | Caractere | 1-1   | 14      | 1.0    |
| 4           | NomeUsuarioAudit      | Nome do usuário que está realizando a operação                                                                                                                                                                                                                                                                                                                                                                                                                   | E     | Caractere | 1-1   | 60      | 1.0    |
| 5           | CNPJEmpresa       | CNPJ da Empresa para Autenticação (mesmo CNPJAplicação)                                                                                                                                                                                                                                                                                                                                                                                                             | E     | Caractere | 1-1   | 14      | 1.0    |
| 4           | TipoVeiculo       | Tipo de veículo para calcular valores da rota. Enviar “Caminhao”:<br/>__0:__ Carro <br/>__1:__ Motocicleta __<br/>2:__ Onibus __<br/>3:__ Caminhao                                                                                                                                                                                                                                                                                                  | E     | Inteiro   | 1-1   | 1       | 1.0    |
| 5           | QtdEixos          | Quantidade de eixos do veículos                                                                                                                                                                                                                                                                                                                                                                                                                     | E     | Inteiro   | 1-1   | 2       | 1.0    |
| 6           | ExibirDetalhes    | Enviar sempre TRUE (default TRUE)                                                                                                                                                                                                                                                                                                                                                                                                                   | E     | Boolean   | 1-0   | True/False | 1.0   |
| 7           | Billing           | Billing                                                                                                                                                                                                                                                                                                                                                                                                                                             | E     | Caractere | 1-0   | 50      | 1.8    |
| 8           | TipoRota          | Enum de tipo de rota para calcular valores da rota. podendo ser:<br/>__0:__ THE_FASTEST (Valor padrão quando não for informado o campo); <br/>__1:__ THE_SHORTEST.                                                                                                                                                                                                                                                                                  | E     | Inteiro   | 1-0   | 1       | 1.0    |
| 9           | Localizacoes      | Lista com até 20 pontos de passagem do veículo para cálculo da rota. <br/>São obrigatórios ao menos dois pontos e podem ser adicionados até 18 pontos intermediários para aumentar a precisão da rota calculada, deixando-a o mais coerente possível com o TMS da transportadora cliente. <br/>A rota é calcular respeitando a ordem da lista indicada. Sendo o primeiro ponto, o local de partida do veículo, e o ultimo o ponto final da entrega  | A     |           | 1-1   |         | 1.0    |
| 9.1         | IbgeCidade        | Código IBGE da cidade __OU__                                                                                                                                                                                                                                                                                                                                                                                                                        | E     | Inteiro   | 1-1   | 7       | 1.0    |
| 9.2         | Latitude          | Latitude do Local (Exemplo -29.8008)                                                                                                                                                                                                                                                                                                                                                                                                                | E     | Decimal   | 1-0   | 10.8    | 1.0    |
| 9.3         | Longitude         | Longitude do Local (Exemplo -47.1001) __OU__                                                                                                                                                                                                                                                                                                                                                                                                        | E     | Decimal   | 1-0   | 10.8    | 1.0    |
| 9.4         | IdRotaModelo      | Consulta Custo de Rota baseado em Rota previamente cadastrada para o cliente                                                                                                                                                                                                                                                                                                                                                                        | E     | Inteiro   | 1-0   | 10      | 1.0    |
| 9.5         | NomeRota          | Nome da rota                                                                                                                                                                                                                                                                                                                                                                                                                                        | E     | Caractere | 1-0   | 100     | 1.0    |


<br>

## <b>Modelo de Json - Request</b>

<br>

```json
{
  "CNPJAplicacao": "",
  "CNPJEmpresa": "",
  "Token": "",
  "TipoVeiculo": 1,
  "QtdEixos": 0,
  "ExibirDetalhes": true,
  "Billing": "",
  "TipoRota": 1,
  "Localizacoes": [
    {
      "IbgeCidade": ""
      "Latitude": 0.0,
      "Longitude": 0.0,
      "IdRotaModelo": 0,
      "NomeRota": ""
    },
  ]
}
```
<br>

## <b>Response</b>

<br>

| Índice      | Chave                      | Descrição                                                                                                                                                                         | Elem. | Tipo      | Ocor. | Tamanho     | Versão |
|-------------|----------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------|-----------|-------|-------------|--------|
| 1           | Sucesso                    | Enum do Status do processamento:<br/>__0:__ Falha,<br/>__1:__ Sucesso                                                                                                             | E     | Inteiro   | 1-1   | 1           | 1.0    |
| 2           | Mensagem                   | Mensagem de retorno                                                                                                                                                               | E     | Caractere | 1-1   | 100         | 1.0    |
| 3           | CustoTotal                 | Valor total do percurso solicitado                                                                                                                                                | E     | Inteiro   | 1-1   | 10          | 1.0    |
| 4           | CustoTotalTAG              | Valor total do percurso solicitado com desconto concedido nas concessionárias (acordo comercial entre TAG e concessionarias)                                                      | E     | Inteiro   | 1-1   | 10          | 1.0    |
| 5           | IdentificadorHistorico     | Código único que identifica a roteirização. No processo de integração da viagem, para efetivar a compra do pedágio, é necessário enviar esta informação no objeto “Pedágio”       | E     | GUID      | 1-1   | 40          | 1.0    |
| 6           | KmTotal                    | Quilometragem (Km) total da rota                                                                                                                                                  | E     | Decimal   | 1-1   | 40          | 1.0    |
| 7           | TempoPrevisto              | Tempo total da rota                                                                                                                                                               | E     | Caractere | 1-1   | HH:mm:ss    | 1.0    |
| 8           | Localizacoes               | Localizações da rota                                                                                                                                                              | A     |           | 1-1   |             |        |
| 8.1         | cidade                     | Nome da cidade com todas letras em maiusculo                                                                                                                                      | E     | Caractere | 0-1   | 100         | 1.0    |
| 8.2         | estado                     | Sigla do estado com todas letras em maiusculo                                                                                                                                     | E     | Caractere | 0-1   | 2           | 1.0    |
| 8.3         | pais                       | Nome do País com todas letras em maiusculo                                                                                                                                        | E     | Caractere | 0-1   | 100         | 1.0    |
| 9           | Pracas                     | Lista de praças                                                                                                                                                                   | A     |           | 1-1   |             |        |
| 9.1         | nome                       | Nome da praça de pedágio                                                                                                                                                          | E     | Caractere | 1-1   | 200         | 1.0    |
| 9.2         | telefone                   | Telefone de contado                                                                                                                                                               | E     | Caractere | 0-1   | 20          | 1.0    |
| 9.3         | enderecoDescricao          | Endereço da Praça de Pedágio                                                                                                                                                      | E     | Caractere | 0-1   | 200         | 1.0    |
| 9.4         | concessao                  | Concessionaria / Concessão                                                                                                                                                        | E     | Caractere | 0-1   | 200         | 1.0    |
| 9.5         | codigoAntt                 | Código da ANTT referente a praça                                                                                                                                                  | E     | Inteiro   | 1-1   | 10          | 1.0    |
| 9.6         | viaFacilId                 | ID do Via Facil                                                                                                                                                                   | E     | Inteiro   | 1-1   | 10          | 1.0    |
| 9.7         | fragmentoIndex             | Indexador                                                                                                                                                                         | E     | Inteiro   | 1-1   | 10          | 1.0    |
| 9.8         | localizacao                | Localização da praça                                                                                                                                                              | G     |           | 1-1   |             |        |
| 9.8.1       | latitude                   | Coordenada geográfica da praça de pedágio                                                                                                                                         | E     | Decimal   | 0-1   | 11,8        | 1.0    |
| 9.8.2       | longitude                  | Coordenada geográfica da praça de pedágio                                                                                                                                         | E     | Decimal   | 0-1   | 11,8        | 1.0    |
| 9.9         | precos                     | Lista de preços do pedágio. Para o Brasil sempre                                                                                                                                  | A     |           | 1-1   |             |        |
| 9.9.1       | precoEixoAdicional         | Valor Eixo Adicional                                                                                                                                                              | E     | Decimal   | 1-1   | 10,2        | 1.0    |
| 9.9.2       | valor                      | Valor total a ser pago para a quantidade de eixos requisitada                                                                                                                     | E     | Decimal   | 1-1   | 10,2        | 1.0    |
| 9.9.3       | valorTAG                   | Valor total a ser pago para a quantidade de eixos requisitada, valor TAG com desconto , se concedido pela concessionária                                                          | E     | Decimal   | 1-1   | 10,2        | 1.0    |

<br>

## <b>Modelo de Json - Response</b>

<br>

```json
{
  "Status": 0,
  "Mensagem": null,
  "CustoTotal": 0.0,
  "CustoTotalTag": 0.0,
  "IdentificadorHistorico": "",
  "KmTotal": 0,
  "TempoPrevisto": "00:00:00",
  "Localizacoes": [
    {
      "cidade": "",
      "estado": "",
      "pais": ""
    }
  ]
  "Pracas": [
    {
      "nome": "",
      "localizacao": {
        "latitude": "",
        "longitude": ""
      },
      "telefone": "",
      "enderecoDescricao": "",
      "concessao": "",
      "codigoAntt": "",
      "viaFacilId": "",
      "fragmentoIndex": 0,
      "precos": [
        {
          "precoEixoAdicional": 0.0,
          "valor": 0.0,
          "valorTag": 0.0
        }
      ]
    }
  ]
}
```