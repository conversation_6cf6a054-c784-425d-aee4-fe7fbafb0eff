<h1> <center> <b>DOCUMENTAÇÃO API EXTRATTA</b> </center> </h1>

Este documento é destinado a profissionais de TI (desenvolvedores de sistemas) e tem 
como objetivo detalhar os métodos para realizar a integração com a API da EXTRATTA para:

+ __Emissão de CIOT__ (Padrão e/ou TAC Agregado)
+ __PEF__ - Pagamento Eletrônico de Frete (Cartão Extratta)
+ __VPO__ - Vale Pedágio Obrigatório (HUB de Vale Pedágio)
+ __Diárias__ - Despesas de Viagem (Cartão Extratta)

Em relação aos fornecedores de vale-pedágio (VPO), trabalhamos com os seguintes fornecedores:

+ __TAG Sem parar:__ CNPJ 04.088.208/0001-65 - CGMP - CENTRO DE GESTÃO DE MEIOS DE PAGAMENTO LTDA;
+ __TAG Move Mais:__ CNPJ 15.266.912/0001-87 - MOVE MAIS MEIOS DE PAGAMENTO LTDA;
+ __TAG VELOE:__ CNPJ 04.740.876/0001-25 - ALELO S/A (VELOE);
+ __TAG Extratta by Move Mais:__ CNPJ 15.266.912/0001-87 - MOVE MAIS MEIOS DE PAGAMENTO LTDA;
+ __TAG ConectCar:__ CNPJ 16.577.631/0002-99 - CONECTCAR INSTITUIÇÃO DE PAGAMENTO E SOLUÇÕES DE MOBILIDADE ELETRÔNICA S.A;
+ __TAG Taggy Edenred:__ CNPJ 36.000.836/0001-33 - EXTRATTA MEIOS DE PAGAMENTO LTDA;
+ __Moedeiro:__ Compra VPO em cartão foi descontinuada e não está mais disponível para novas transações.

<br> <center><h2> REQUISITOS DE COMUNICAÇÃO </h2></center>

   <p> A comunicação entre o cliente e o servidor será feita na arquitetura REST e o formato de dados em JSON. </p>
   <p> Para acesso aos métodos de integração, será necessário que tenha conexão com a internet. </p>
   <p> Nosso protocolo atende as novas exigências da ANTT, comunicando informações através de JSON, onde todas as validações retornadas são oriundas das regras de negócio aplicadas pela ANTT em seus respectivos ambiente. </p>


<br> <center><h2> AUTENTICAÇÃO </h2></center>


Para todas as requisições realizadas no Web Service (API), é necessário que alguns dados sejam enviados para autenticação. Ambos são obrigatórios e a falta ou invalidez de qualquer um deles terá a requisição rejeitada. Os dados são:

+ __CNPJAplicacao:__ CNPJ da aplicação será o CNPJ concedido a empresa para realizar qualquer requisição.
+ __TOKEN:__ Complementando o CNPJ da aplicação, o token será uma chave concedida pela EXTRATTA. Este token é único para cada CNPJAplicação. 
+ __CNPJEmpresa:__ CNPJ da empresa será o CNPJ concedido a empresa para realizar qualquer requisição, deve ser utilizado o mesmo CNPJAplicacao. 


<center> <b>Cabeçalho</b> </center>

| Chave         | Descrição                                                                 | Elem. | Tipo      | Ocor. | Tamanho | Versão |
|---------------|---------------------------------------------------------------------------|-------|-----------|-------|---------|--------|
| CNPJAplicacao | CNPJ da Aplicação Para autenticação                                       | E     | Caractere | 1-1   | 14      | 1.0    |
| Token         | Gerado a partir do CNPJ de autenticação para validar permissões de acesso | E     | Caractere | 1-1   | 100     | 1.0    |
| CNPJEmpresa   | CNPJ da Empresa para Autenticação ( mesmo    CNPJAplicação )              | E     | Caractere | 1-1   | 100     | 1.0    |


<br> <center><h2> COMUNICAÇÃO - API </h2></center>

#### __API de Homologação:__ 

<br>

Para realizar integrações em base de HOMOLOGAÇÃO, são disponibilizados os link’s abaixo:

<br>

   *  __API para integrações entre sistemas:__ http://apiho.extratta.com.br:50063/ 
   *  __Acesso ao Portal Web/Site:__ http://homolog.extratta.com.br:8024/ 
   *  __Impressão do CIOT:__ http://apiho.extratta.com.br:1000/Ciot/ServicesFull/Reports/DeclaracaoTransporte/{0}/{1} 
   *  __Impressão do Recibo Vale-Pedágio:__ http://apiho.extratta.com.br:50063/ViagemAts/ReciboVPO/IdViagem 
   *  __Impressão do Recibo Pagamento de Frete:__ http://apiho.extratta.com.br:50063/ViagemAts/ReciboPEF?json={IdViagem:idViagem,ListarParcelasCanceladas:false} 

<br>

#### __API de Produção:__

<br>

Após o processo validado/homologado deve ser solicitado as credenciais para operação em PRODUÇÃO:
 
   * __URL / API:__ https://api.extratta.com.br:2002/ 
   * __Acesso a Plataforma:__ http://portal.extratta.com.br/login 
   * __Impressão CIOT:__ https://apims.extratta.com.br/Ciot/ServicesFull/Reports/DeclaracaoTransporte/{0}/{1} 
   * __Impressão do Recibo Vale-Pedágio:__ https://api.extratta.com.br:2110/ViagemAts/ReciboVPO/IdViagem 
   * __Impressão do Recibo Pagamento de Frete:__ https://api.extratta.com.br:2110/ViagemAts/ReciboPEF?json=%7BIdViagem: idViagem,ListarParcelasCanceladas:false%7D
 

 <p>{0} Número do CIOT</p>
 <p>{1} Senha do CIOT </p>


<br> <h2><b><center> SIGLAS E ABREVIATURAS </center></b></h2>

<br> <center>ELEMENTOS</center>


| SIGLA | DESCRIÇÃO                                                                                         |
|-------|---------------------------------------------------------------------------------------------------|
| E     | Elemento: Tag referente a um campo único dentro do Json                                           |
| G     | Grupo: Tag referente a objeto único dentro do Json, envolvendo diversos elementos dentro de si.   |
| A     | Array: Tag referente a uma lista de objetos dentro do Json                                        |


<br> <center> OCORRÊNCIA </center>


| OCORRÊNCIA | DESCRIÇÃO                                                |
|------------|----------------------------------------------------------|
| 1-1        | Indicação de que a TAG é obrigatória no envio ou retorno |
| 1-0        | Indicação de que a TAG é opcional no envio               |
| 0-1        | Indicação de que a TAG é opcional no retorno             |




<br> <center><h2> CONTATO/SUPORTE </h2></center>

Canais de comunicação:

> Telefone / WhatsApp : (49) 3425-9650

> E-mail : <EMAIL> 